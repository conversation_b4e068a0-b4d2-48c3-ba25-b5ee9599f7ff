// Login and Passcode Management

class Login {
    constructor() {
        // Guarantee apiService is up-to-date and has phonenoVerification

        if (!window.apiService || typeof window.apiService.phonenoVerification !== 'function') {
            if (typeof ApiService === 'function') {
                window.apiService = new ApiService();
                console.warn('window.apiService was missing or outdated. Re-instantiated.');
            } else {
                throw new Error('ApiService class is not defined. Check if api-service.js loaded.');
            }
        }
        this.apiService = window.apiService;
        // Debug: List all methods on apiService
        // console.log('apiService methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.apiService)));
        this.loginForm = document.getElementById('loginForm');
        this.loginError = document.getElementById('loginError');
        this.init();
    }

    init() {
        // Check if user is already logged in via session
        const fromPasscode = sessionStorage.getItem('fromPasscode') === 'true';

        // Check if user has persistent login in localStorage
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        const hasPasscode = localStorage.getItem('hasPasscode') === 'true';
        const loginTimestamp = parseInt(localStorage.getItem('loginTimestamp') || '0');
        const now = Date.now();
        const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000;

        // If user has valid persistent login, set session flag and redirect
        if (isLoggedIn && hasPasscode && loginTimestamp && (now - loginTimestamp < THIRTY_DAYS_MS)) {
            // Set session flag to bypass passcode screen
            sessionStorage.setItem('fromPasscode', 'true');
            sessionStorage.setItem('accessedMainApp', 'true');
            window.location.replace('membership.html');
            return;
        }

        // If coming from passcode screen in current session
        if (fromPasscode) {
            window.location.replace('membership.html');
            return;
        }

        // Attach event listener
        this.loginForm.addEventListener('submit', this.handleLogin.bind(this));
    }

    async handleLogin(e) {
        e.preventDefault();
        const phone = document.getElementById('phone').value.trim();
        // Simple validation
        if (!phone) {
            this.loginError.textContent = 'Please enter your phone number';
            return;
        }
        // Call phone verification API
        this.loginError.textContent = '';
        try {
            // console.log('Verifying phone number:', phone);
            const response = await this.apiService.phonenoVerification(phone);
            // console.log('Phone verification response:', response.data['name']);
            // Store phone number in localStorage(optionally encrypted)`

            // const encryptedPhone = CryptoUtils.encrypt(phone);

            localStorage.setItem('userPhone', phone);


            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('hasPasscode', 'true');
            localStorage.setItem('name', response.data['name']);
            localStorage.setItem('staff_id', response.data['staff_id']);
            localStorage.setItem('loginTimestamp', Date.now().toString());
            this.loginError.textContent = 'Phone verified! Redirecting...';

            if (response.data.is_passcode_set === true) {
                setTimeout(() => {
                    window.location.replace('passcode.html');
                }, 1000);
            } else if (response.data.is_passcode_set === false) {
                setTimeout(() => {
                    window.location.replace('passcode-setup.html');
                }, 1000);
            } else {
                this.loginError.textContent = 'Unexpected server response. Please contact support.';
            }
        } catch (error) {

            if (error.response && error.response.status === 404) {
                this.loginError.textContent = 'Phone number not found';
            } else if (error.message && error.message.includes('404')) {
                this.loginError.textContent = 'Phone number not found';
            } else {
                this.loginError.textContent = error.message || 'Phone verification failed. Please try again.';
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new Login();
});
