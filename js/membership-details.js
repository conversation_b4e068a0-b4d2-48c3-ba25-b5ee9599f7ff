// membership-details.js
// Contains only the membership user details methods, moved from membership.html
// No code changes, only moved as requested.

async function loadMembershipDetails(apiService, membershipGrid, user, displayMembershipDetails) {
    membershipGrid.innerHTML = `<div class="loading">${window.AppText.loadingMembership}</div>`;
    try {
        if (!apiService) apiService = window.apiService;
        const response = await apiService.getMembershipDetails(user.name);
        displayMembershipDetails(response.data);
    } catch (error) {
        console.error('Error fetching membership details:', error);
        if (error.response && error.response.status === 404) {
            membershipGrid.innerHTML = `<div class="error">${window.AppText.failedLoadMembership}</div>`;
        } else if (error.message && error.message.includes('404')) {
            membershipGrid.innerHTML = `<div class="error">${window.AppText.failedLoadMembership}</div>`;
        } else {
            membershipGrid.innerHTML = `<div class="error">No membership details found for <span style="color: #3b82f6; font-weight: 600;">${user.name}</span></div>`;
        }
    }
}

function displayMembershipDetails(data, membershipGrid, formatCheckInDateTime, calculateDuration, handleCheckIn) {
    // console.log('Membership data received:', data);
    let memberships = [];
    let userData = null;
    if (data && data.user) userData = data.user;
    if (data && data.membership && Array.isArray(data.membership)) {
        memberships = data.membership;
    } else if (Array.isArray(data)) {
        memberships = data;
    } else if (data) {
        memberships = [data];
    }
    // console.log('Processed memberships array:', memberships);
    if (memberships.length === 0) {
        membershipGrid.innerHTML = `<div class="no-results">${window.AppText.noMembershipFound}</div>`;
        return;
    }
    let userInfoHtml = '';
    if (userData) {
        const isCheckedInGym = userData.recent_check_ins_gym && userData.recent_check_ins_gym.length > 0 && userData.recent_check_ins_gym[0].is_checked_in_today;
        const isCheckedInTT = userData.recent_check_ins_tt && userData.recent_check_ins_tt.length > 0 && userData.recent_check_ins_tt[0].is_checked_in_today;
        const gymCheckIn = isCheckedInGym ? userData.recent_check_ins_gym[0] : null;
        const ttCheckIn = isCheckedInTT ? userData.recent_check_ins_tt[0] : null;
        let checkInInfoHtml = '';
        if (gymCheckIn) checkInInfoHtml += `<div>Gym check-in: ${gymCheckIn.check_in_date} at ${gymCheckIn.check_in_time}</div>`;
        if (ttCheckIn) checkInInfoHtml += `<div>Table Tennis check-in: ${ttCheckIn.check_in_date} at ${ttCheckIn.check_in_time}</div>`;
        userInfoHtml = `
            <div class="user-info-card">
                <div class="user-info-header">
                    <div class="user-info-left">
                        <div class="user-avatar">${userData.name ? userData.name.charAt(0).toUpperCase() : ''}</div>
                        <div class="user-name">${userData.name || 'Member'}</div>
                    </div>
                    <div class="user-info-right">
                        <div class="checkin-buttons">
                            ${isCheckedInTT ? `
                            <div class="activity-status table-tennis-status">
                                <span class="activity-icon checked-in">🏓</span>
                            </div>` : `
                            <button class="icon-button table-tennis" data-activity="Table Tennis" data-user-name="${userData.name}" data-user-id="${userData.id || ''}">
                                🏓
                            </button>`}
                            ${isCheckedInGym ? `
                            <div class="activity-status gym-status">
                                <span class="activity-icon checked-in">🏋🏻‍♂️</span>
                            </div>` : `
                            <button class="icon-button gym" data-activity="Gym" data-user-name="${userData.name}" data-user-id="${userData.id || ''}">
                                🏋🏻‍♂️
                            </button>`}
                        </div>
                    </div>
                </div>
                <div class="user-info-details">
                    ${userData.phone_number ? `<div class="user-detail"><span class="detail-label">${window.AppText.phoneLabel}</span> <a href="tel:${userData.phone_number}" class="phone-link">${userData.phone_number} <span class="call-icon">📞</span></a></div>` : ''}
                    ${userData.email ? `<div class="user-detail"><span class="detail-label">${window.AppText.emailLabel}</span> <span style="word-break: break-word; overflow-wrap: break-word;">${userData.email}</span></div>` : ''}
                    <div class="check-in-status-container">
                        ${gymCheckIn ? `
                        <div class="check-in-status gym-check-in">
                            <div class="check-in-icon">🏋🏻‍♂️</div>
                            <div class="check-in-details">
                                <div class="check-in-activity">Gym</div>
                                <div class="check-in-time">${formatCheckInDateTime(gymCheckIn.check_in_date, gymCheckIn.check_in_time)}</div>
                            </div>
                        </div>` : ''}
                        ${ttCheckIn ? `
                        <div class="check-in-status tt-check-in">
                            <div class="check-in-icon">🏓</div>
                            <div class="check-in-details">
                                <div class="check-in-activity">Table Tennis</div>
                                <div class="check-in-time">${formatCheckInDateTime(ttCheckIn.check_in_date, ttCheckIn.check_in_time)}</div>
                            </div>
                        </div>` : ''}
                    </div>
                </div>
            </div>
        `;
    }
    let html = userInfoHtml;
    html += memberships.map(membership => `
        <div class="membership-card" data-membership-id="${membership.membership_id || ''}">
            <div class="membership-header">
                <div class="membership-title">${membership.plan_name}</div>
                <div class="membership-header-right">
                    <div class="membership-status status-${membership.status}">
                        ${membership.status}
                    </div>
                    <div class="membership-toggle">
                        <svg class="toggle-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="membership-details-container collapsed">
                <div class="membership-detail">
                    <div class="membership-detail-label">Start Date:</div>
                    <div class="membership-detail-value">${new Date(membership.start_date).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    })}</div>
                </div>
                <div class="membership-detail">
                    <div class="membership-detail-label">End Date:</div>
                    <div class="membership-detail-value">${new Date(membership.end_date).toLocaleDateString('en-US', {
        year: 'numeric', month: 'long', day: 'numeric'
    })}</div>
                </div>
                <div class="membership-detail">
                    <div class="membership-detail-label">Duration:</div>
                    <div class="membership-detail-value">${calculateDuration(membership.start_date, membership.end_date)}</div>
                </div>
                <div class="membership-detail">
                    <div class="membership-detail-label">Amount:</div>
                    <div class="membership-detail-value">₹${(membership.amount).toLocaleString()}</div>
                </div>
                <div class="membership-detail">
                    <div class="membership-detail-label">Transaction:</div>
                    <div class="membership-detail-value">${membership.transaction_type.charAt(0).toUpperCase() + membership.transaction_type.slice(1)}</div>
                </div>
                <div class="membership-detail">
                    <div class="membership-detail-label">Coaching Plan:</div>
                    <div class="membership-detail-value">${membership.is_coaching_plan === 'yes' ? 'Yes' : 'No'}</div>
                </div>
            </div>
        </div>
    `).join('');
    membershipGrid.innerHTML = html;
    const membershipButtons = membershipGrid.querySelectorAll('.icon-button');
    membershipButtons.forEach(button => {
        button.addEventListener('click', async (event) => {
            event.preventDefault();
            event.stopPropagation();
            const userName = button.getAttribute('data-user-name');
            const userID = button.getAttribute('data-user-id');
            const activity = button.getAttribute('data-activity');
            await handleCheckIn(userName, userID, button, activity);
        });
    });
    initMembershipCardToggle(membershipGrid);
}

function calculateDuration(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const months = Math.floor(diffDays / 30);
    const days = diffDays % 30;
    if (months > 0) {
        return days > 0 ? `${months} months, ${days} days` : `${months} months`;
    }
    return `${days} days`;
}

function initMembershipCardToggle(membershipGrid) {
    const membershipCards = membershipGrid.querySelectorAll('.membership-card');
    membershipCards.forEach(card => {
        const header = card.querySelector('.membership-header');
        const detailsContainer = card.querySelector('.membership-details-container');
        if (header && detailsContainer) {
            detailsContainer.style.maxHeight = '0px';
            header.addEventListener('click', (event) => {
                event.stopPropagation();
                card.classList.toggle('expanded');
                detailsContainer.classList.toggle('collapsed');
                if (detailsContainer.classList.contains('collapsed')) {
                    detailsContainer.style.maxHeight = '0px';
                } else {
                    detailsContainer.style.maxHeight = detailsContainer.scrollHeight + 'px';
                }
            });
        }
    });
}

// Export for use in membership.html
window.membershipDetails = {
    loadMembershipDetails,
    displayMembershipDetails,
    calculateDuration,
    initMembershipCardToggle
};
