// Encryption/Decryption Utilities

/**
 * Simple encryption utility for client-side credential storage
 * Note: This is a basic implementation for demo purposes.
 * For production, consider using more secure methods with proper key management.
 */

// Secret key for encryption from .env file (via server)
let SECRET_KEY = '';

// Function to fetch the encryption key from the server
// async function fetchEncryptionKey() {
//     try {
//         const response = await fetch('/api/encryption-key');
//         if (!response.ok) {
//             throw new Error('Failed to fetch encryption key');
//         }

//         const data = await response.json();
//         return data.key;
//     } catch (error) {
//         console.error('Error fetching encryption key:', error);
//         // Fallback to a default key if server request fails
//         return "TopSpinClub2025SecureKey";
//     }
// }

// Initialize the encryption key
// (async function () {
//     SECRET_KEY = await fetchEncryptionKey();
//     // console.log('Encryption key initialized from .env file');
// })();

/**
 * Encrypts a string value using AES
 * @param {string} value - The value to encrypt
 * @returns {string} - The encrypted value
 */
function encrypt(value) {
    if (!value) return '';

    try {
        // Create a simple encryption by XORing with the secret key
        let result = '';
        for (let i = 0; i < value.length; i++) {
            const charCode = value.charCodeAt(i) ^ SECRET_KEY.charCodeAt(i % SECRET_KEY.length);
            result += String.fromCharCode(charCode);
        }

        // Convert to base64 for safe storage
        return btoa(result);
    } catch (error) {
        console.error('Encryption error:', error);
        return '';
    }
}

/**
 * Decrypts an encrypted string value
 * @param {string} encryptedValue - The encrypted value to decrypt
 * @returns {string} - The decrypted value
 */
function decrypt(encryptedValue) {
    if (!encryptedValue) return '';

    try {
        // Decode from base64
        const base64Decoded = atob(encryptedValue);

        // Reverse the XOR operation
        let result = '';
        for (let i = 0; i < base64Decoded.length; i++) {
            const charCode = base64Decoded.charCodeAt(i) ^ SECRET_KEY.charCodeAt(i % SECRET_KEY.length);
            result += String.fromCharCode(charCode);
        }

        return result;
    } catch (error) {
        console.error('Decryption error:', error);
        return '';
    }
}

// Export the encryption utilities
window.CryptoUtils = {
    encrypt,
    decrypt
};
