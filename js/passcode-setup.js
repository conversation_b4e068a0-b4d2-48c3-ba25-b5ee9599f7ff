// Passcode Setup Functionality

// console.log("api-service.js loaded?", typeof window.ApiService, typeof window.apiService);

document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    if (!isLoggedIn) {
        // Redirect to login if not logged in

        window.location.replace('login.html');
        return;
    }

    // Get DOM elements
    const setupContainer = document.getElementById('setupContainer');
    const confirmContainer = document.getElementById('confirmContainer');
    const passcodeMessage = document.getElementById('passcodeMessage');
    const setPasscodeBtn = document.getElementById('setPasscodeBtn');
    const confirmPasscodeBtn = document.getElementById('confirmPasscodeBtn');

    // Setup passcode input auto-focus behavior for initial setup
    const passcodeInputs = document.querySelectorAll('.passcode-digit:not(.confirm)');
    setupPasscodeInputBehavior(passcodeInputs);

    // Setup passcode input auto-focus behavior for confirmation
    const confirmInputs = document.querySelectorAll('.passcode-digit.confirm');
    setupPasscodeInputBehavior(confirmInputs);

    // Store the initial passcode temporarily
    let tempPasscode = '';

    // Handle set passcode button click
    setPasscodeBtn.addEventListener('click', () => {
        // Get passcode from inputs
        tempPasscode = Array.from(passcodeInputs).map(input => input.value).join('');

        // Validate passcode
        if (tempPasscode.length !== 4 || !/^\d{4}$/.test(tempPasscode)) {
            passcodeMessage.textContent = 'Please enter a valid 4-digit passcode';
            passcodeMessage.className = 'passcode-error';
            return;
        }

        // Show confirmation screen
        setupContainer.style.display = 'none';
        confirmContainer.style.display = 'block';
        confirmInputs[0].focus();
        passcodeMessage.textContent = '';
    });

    // Handle confirm passcode button click
    confirmPasscodeBtn.addEventListener('click', () => {
        // Get confirmation passcode
        const confirmPasscode = Array.from(confirmInputs).map(input => input.value).join('');

        // Validate confirmation matches
        if (confirmPasscode !== tempPasscode) {
            passcodeMessage.textContent = 'Passcodes do not match. Please try again.';
            passcodeMessage.className = 'passcode-error';

            // Reset confirmation inputs
            confirmInputs.forEach(input => input.value = '');
            confirmInputs[0].focus();
            return;
        }

        // Save encrypted passcode

        localStorage.setItem('hasPasscode', 'true');

        // Decrypt phone number from localStorage
        let phone = localStorage.getItem('userPhone');

        // Diagnostics before API call
        if (!window.ApiService) {
            console.error("ApiService class missing from window!");
        }
        if (!window.apiService) {
            console.error("apiService instance missing from window!");
        }
        if (window.apiService && typeof window.apiService.createPasscode !== 'function') {
            console.error("apiService.createPasscode is not a function!", window.apiService);
        }
        // Defensive: Guarantee apiService is present and up-to-date
        if (!window.apiService || typeof window.apiService.createPasscode !== 'function') {
            if (typeof ApiService === 'function') {
                window.apiService = new ApiService();
                console.warn('window.apiService was missing or outdated. Re-instantiated.');
            } else {
                passcodeMessage.textContent = 'ApiService is not defined. Please reload the page.';
                passcodeMessage.className = 'passcode-error';
                return;
            }
        }
        // Call createPasscode API
        window.apiService.createPasscode(phone, tempPasscode)
            .then(response => {
                // console.log('Passcode set response:', response);
                // Show success message
                passcodeMessage.textContent = 'Passcode set successfully!';
                passcodeMessage.className = 'passcode-success';

                // Set session flag and redirect to main app after short delay
                setTimeout(() => {
                    // Set session flags for current session
                    sessionStorage.setItem('fromPasscode', 'true');
                    sessionStorage.setItem('accessedMainApp', 'true');

                    // Update timestamp in localStorage for persistent login
                    localStorage.setItem('loginTimestamp', Date.now().toString());

                    window.location.replace('membership.html');
                }, 1500);
            })
            .catch(error => {
                passcodeMessage.textContent = error.message || 'Failed to set passcode on server.';
                passcodeMessage.className = 'passcode-error';
            });
    });
});

// Function to handle input behavior for passcode digits
function setupPasscodeInputBehavior(inputs) {
    inputs.forEach((input, index) => {
        // Auto-focus next input when a digit is entered
        input.addEventListener('input', (e) => {
            if (e.target.value.length === 1) {
                // Move to next input if available
                if (index < inputs.length - 1) {
                    inputs[index + 1].focus();
                }
            }
        });

        // Handle backspace to go to previous input
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Backspace' && e.target.value === '' && index > 0) {
                inputs[index - 1].focus();
            }
        });

        // Ensure only numbers are entered
        input.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    });
}
