// Passcode Verification Functionality

document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    const hasPasscode = localStorage.getItem('hasPasscode') === 'true';

    if (!isLoggedIn) {
        // Redirect to login if not logged in
        window.location.replace('login.html');
        return;
    }

    if (!hasPasscode) {
        // Redirect to passcode setup if no passcode is set
        window.location.replace('passcode-setup.html');
        return;
    }

    // Get DOM elements
    const passcodeInputs = document.querySelectorAll('.passcode-digit');
    const verifyPasscodeBtn = document.getElementById('verifyPasscodeBtn');
    const passcodeMessage = document.getElementById('passcodeMessage');
    const forgotPasscodeLink = document.getElementById('forgotPasscodeLink');

    // Setup passcode input auto-focus behavior
    setupPasscodeInputBehavior(passcodeInputs);

    // Handle verify passcode button click
    verifyPasscodeBtn.addEventListener('click', () => {
        verifyPasscode();
    });

    // Allow enter key to submit
    passcodeInputs.forEach(input => {
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                verifyPasscode();
            }
        });
    });

    // Handle forgot passcode link
    forgotPasscodeLink.addEventListener('click', (e) => {
        e.preventDefault();

        // For demo purposes, just clear localStorage and redirect to login
        if (confirm('This will log you out and you will need to login again. Continue?')) {

            localStorage.removeItem('hasPasscode');
            // localStorage.removeItem('isLoggedIn');
            window.location.replace('passcode-setup.html');
        }
    });

    // Function to verify entered passcode using API
    async function verifyPasscode() {
        // Get entered passcode
        const enteredPasscode = Array.from(passcodeInputs).map(input => input.value).join('');

        // Validate passcode format
        if (enteredPasscode.length !== 4 || !/^\d{4}$/.test(enteredPasscode)) {
            passcodeMessage.textContent = 'Please enter a valid 4-digit passcode';
            passcodeMessage.className = 'passcode-error';
            return;
        }

        // Decrypt phone number from localStorage
        let phone = localStorage.getItem('userPhone');

        // Defensive: Guarantee apiService is present and up-to-date
        if (!window.apiService || typeof window.apiService.validatePasscode !== 'function') {
            if (typeof ApiService === 'function') {
                window.apiService = new ApiService();
            } else {
                passcodeMessage.textContent = 'ApiService is not defined. Please reload the page.';
                passcodeMessage.className = 'passcode-error';
                return;
            }
        }

        // Call validatePasscode API
        try {
            // console.log(enteredPasscode);
            const response = await window.apiService.validatePasscode(phone, enteredPasscode);

            // console.log(response.data['status']);
            if (response && response.data && response.data['status'] === true) {
                // Show success message
                passcodeMessage.textContent = 'Success! Redirecting...';
                passcodeMessage.className = 'passcode-success';
                setTimeout(() => {
                    // Set session flags for current session
                    sessionStorage.setItem('fromPasscode', 'true');
                    sessionStorage.setItem('accessedMainApp', 'true');

                    // Update timestamp in localStorage for persistent login
                    localStorage.setItem('loginTimestamp', Date.now().toString());

                    window.location.replace('membership.html');
                }, 1000);
            } else {
                passcodeMessage.textContent = response.data.message || 'Incorrect passcode. Please try again.';
                passcodeMessage.className = 'passcode-error';
                passcodeInputs.forEach(input => input.value = '');
                passcodeInputs[0].focus();
            }
        } catch (error) {
            passcodeMessage.textContent = error.message || 'Failed to validate passcode.';
            passcodeMessage.className = 'passcode-error';
            passcodeInputs.forEach(input => input.value = '');
            passcodeInputs[0].focus();
        }
    }
});

// Function to handle input behavior for passcode digits
function setupPasscodeInputBehavior(inputs) {
    inputs.forEach((input, index) => {
        // Auto-focus next input when a digit is entered
        input.addEventListener('input', (e) => {
            if (e.target.value.length === 1) {
                // Move to next input if available
                if (index < inputs.length - 1) {
                    inputs[index + 1].focus();
                }
            }
        });

        // Handle backspace to go to previous input
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Backspace' && e.target.value === '' && index > 0) {
                inputs[index - 1].focus();
            }
        });

        // Ensure only numbers are entered
        input.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    });
}
