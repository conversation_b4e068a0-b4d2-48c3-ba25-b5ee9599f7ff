/**
 * Static text content for the TopSpin Club Membership Tracker application
 * Centralizes all text strings for easier maintenance and future localization
 */
const AppText = {
    // Page title and headers
    pageTitle: "Check-in & Membership",
    appTitle: "TopSpin Club Membership Tracker",
    appTitleMobile: "Club Member Tracker",

    // Search section
    searchLabel: "Search Member To Get Membership Details",
    searchPlaceholder: "Type 3+ characters to search by name/phone…",

    // Check-in section
    checkinsTitle: "Today's Check-ins",
    loadingCheckins: "Loading check-in details...",
    noCheckinsAvailable: "No check-ins available",
    failedLoadCheckins: "Failed to load recent check-ins",

    // Membership section
    membershipTitle: "Membership Details",
    selectMemberPrompt: "Select a member from search results to view their membership details",
    loadingMembership: "Loading membership details...",
    noMembershipFound: "No membership details found for this member",
    failedLoadMembership: "Failed to load membership details",

    // Button text
    checkInButton: "Check In",
    checkedInButton: "Checked In",
    checkingInButton: "Checking in...",
    doneButton: "✓ Done",

    // Activity label
    activityLabel: "Activity:",

    // Detail labels
    phoneLabel: "Phone:",
    emailLabel: "Email:",
    lastCheckinLabel: "Last Check-in:",
    startDateLabel: "Start Date:",
    endDateLabel: "End Date:",
    durationLabel: "Duration:",
    amountLabel: "Amount:",
    transactionLabel: "Transaction:",
    coachingPlanLabel: "Coaching Plan:"
};

// Export as a global variable
window.AppText = AppText;