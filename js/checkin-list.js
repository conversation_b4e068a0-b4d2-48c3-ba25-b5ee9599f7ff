// checkin-list.js
// Contains only the check-in user list methods, moved from membership.html
// No code changes, only moved as requested.

async function loadCheckins(apiService, checkinGrid, displayCheckins) {
    try {
        // Use the API service to get recent check-ins
        const response = await apiService.getUserCheckIns();

        // Check if response contains data
        if (response && response.data) {
            // console.log(response.data);
            displayCheckins(response.data);
        } else {
            checkinGrid.innerHTML = `<div class="no-results">${window.AppText.noCheckinsAvailable}</div>`;
        }
    } catch (error) {
        checkinGrid.innerHTML = `<div class="error">${window.AppText.failedLoadCheckins}</div>`;
    }
}

function displayCheckins(checkins, checkinGrid) {
    if (!checkins || checkins.length === 0) {
        checkinGrid.innerHTML = `<div class="no-results">${window.AppText.noCheckinsAvailable}</div>`;
        return;
    }

    const html = checkins.map(checkin => {
        // Get the first character of name for avatar (safely handle empty names)
        const avatarInitial = checkin.name && checkin.name.length > 0 ?
            checkin.name.charAt(0).toUpperCase() : '?';

        // Handle optional email display
        const emailHtml = checkin.email ?
            `<div class="checkin-user-email">${checkin.email}</div>` : '';

        // Handle optional activity display
        const activity = checkin.activity || checkin.Activity;
        const activityHtml = activity && activity.trim() ?
            `<div class="checkin-user-activity"><span class="activity-label">${window.AppText.activityLabel}</span> ${activity}</div>` : '';

        // Check if user has already checked in today
        const isCheckedInToday = checkin.isCheckedInToday === true;
        const buttonText = isCheckedInToday ? window.AppText.checkedInButton : window.AppText.checkInButton;
        const buttonClass = isCheckedInToday ? 'checkin-button checked-in' : 'checkin-button';
        const buttonDisabled = isCheckedInToday ? 'disabled' : '';

        return `
        <div class="checkin-card">
            <div class="checkin-user-header">
                <div class="checkin-avatar">
                    ${avatarInitial}
                </div>
                <div class="checkin-user-info">
                    <div class="checkin-user-name">${checkin.name}</div>
                    <div class="checkin-user-phone">${checkin.phone}</div>
                    ${isCheckedInToday ? `
                    ${activityHtml}
                    <div class="checkin-datetime">
                        <span class="checkin-icon">🕒</span>
                        ${formatCheckInDateTime(checkin.check_in_date, checkin.check_in_time)}
                    </div>
                    ` : ''}
                </div>
                <div class="checkin-buttons">
                    ${isCheckedInToday ? (activity === "Gym" ? '' : `
                    <div class="activity-status table-tennis-status">
                        <span class="activity-icon checked-in">🏓</span>
                    </div>`) : `
                    <div class="activity-status">
                        <button class="icon-button table-tennis" data-activity="Table Tennis" data-user-name="${checkin.name}" data-user-id="${checkin.id || ''}">
                            🏓
                        </button>
                    </div>`}
                    ${isCheckedInToday ? (activity === "Table Tennis" ? '' : `
                    <div class="activity-status gym-status">
                        <span class="activity-icon checked-in">🏋🏻‍♂️</span>
                    </div>`) : `
                    <div class="activity-status">
                        <button class="icon-button gym" data-activity="Gym" data-user-name="${checkin.name}" data-user-id="${checkin.id || ''}">
                            🏋🏻‍♂️
                        </button>
                    </div>`}
                </div>
            </div>
        </div>
        `;
    }).join('');

    checkinGrid.innerHTML = html;
}

function formatCheckInDateTime(date, time) {
    if (!date && !time) return '';
    return `${date || ''} ${time || ''}`.trim();
}

// Export for use in membership.html
window.checkinList = {
    loadCheckins,
    displayCheckins,
    formatCheckInDateTime
};
