class ApiService {
    constructor() {
        console.log(window.location.hostname);
        // Use configuration from config file
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            this.apiurl = 'https://topspinclub-stagapi.devpress.net/api/v1';
            this.token = 'TXdjSGpTSnV0UlNabHBXVlg1SEhwN1prQ25ndkJKcUp2SndrdFVOUFlHdnZnbmozWmo4N1dsZlcxMDUwSC80aw==';
        } else {
            this.apiurl = 'https://api.topspinclub.in/api/v1';
            this.token = 'Yi9UVDE3N3lXQzYwcE92YUJCVXloUT09';
        }
    }
    /**
     * Search users by name or phone number
     * @param {string} query - The search query
     * @returns {Promise<Object>} - Search results
     */
    async searchUsers(query) {
        try {
            // Build the URL with query parameters
            let url = `${this.apiurl}/user-details`;
            url += `?name=${encodeURIComponent(query)}`;

            const response = await fetch(url, {
                headers: {
                    'api-authorization': this.token
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return { data: data.data || data || [] };
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    /**
     * Get membership details for a user
     * @param {string} userName - The user's name (optional)
     * @returns {Promise<Object>} - Membership details
     */
    async getMembershipDetails(userName) {
        try {
            let url = `${this.apiurl}/user-membership-details`;

            if (userName) {
                url += `?name=${encodeURIComponent(userName)}`;
            }

            const response = await fetch(url, {
                headers: {
                    'api-authorization': this.token
                }
            });

            if (!response.ok) {
                // Enhanced error handling with specific error messages
                if (response.status === 404) {
                    throw new Error(`User not found (404): ${userName}`);
                } else if (response.status === 500) {
                    throw new Error(`Server error (500) while fetching membership details`);
                } else {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            }

            const data = await response.json();
            return { data: data.data || data || [] };
        } catch (error) {
            console.error('API Error in getMembershipDetails:', error);
            throw error;
        }
    }

    /**
     * Get user check-in data with optional count and activity filters
     * @param {number} count - Optional number of check-ins to return
     * @param {string} activity - Optional activity filter
     * @returns {Promise<Object>} - Check-in data for users
     */
    async getUserCheckIns(count, activity = '') {
        try {
            // Build URL with query parameters
            let url = `${this.apiurl}/checked-in-users`;
            const params = new URLSearchParams();

            // params.append('count', count);
            // params.append('count', 10);

            if (activity !== null && activity.trim() !== '') {
                params.append('activity', activity);
            }

            // Add query parameters if any exist
            const queryString = params.toString();
            if (queryString) {
                url += `?${queryString}`;
            }

            const response = await fetch(url, {
                headers: {
                    'api-authorization': this.token
                }
            });


            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return { data: data.data || data || [] };
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    /**
     * Record a check-in for a user
     * @param {string} userName - The user's name
     * @param {string} userID - The user's ID (optional)
     * @param {string} activity - The activity type (optional)
     * @param {string} staff_id - The staff ID (optional)
     * @returns {Promise<Object>} - Check-in confirmation
     */
    async checkInUser(userName, userID = null, activity = '', staff_id = '') {
        try {
            const url = `${this.apiurl}/check-in`;

            const requestBody = {
                user_id: userID,
                activity: activity || '', // Ensure activity is always set
                staff_id: staff_id || ''
            };
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'api-authorization': this.token
                },
                body: JSON.stringify(requestBody)
            });
            const data = await response.json();

            // Check both HTTP status and success flag in response
            if (!response.ok || (data.extra_meta && data.extra_meta.success === false)) {
                // Get error message from the response data
                const errorMessage = data.extra_meta?.summary?.title || data.extra_meta?.message || `HTTP error! status: ${response.status}`;

                // Display alert with the error message
                alert(`Check-in failed: ${errorMessage}`);

                const error = new Error(errorMessage);
                error.responseData = data; // Attach the response data to the error for more context
                throw error;
            }

            return { data: data.data || data || {} };
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    /**
     * Record a check-out for a user
     * @param {string} userID - The user's ID (optional)
     * @param {string} activity - The activity type (optional)
     * @param {string} staff_id - The staff ID (optional)
     * @returns {Promise<Object>} - Check-out confirmation
     */
    async checkOutUser(userID = null, activity = '', staff_id = '') {
        try {
            const url = `${this.apiurl}/check-out`;

            const requestBody = {
                user_id: userID,
                activity: activity || '',
                staff_id: staff_id || ''
            };
            console.log(requestBody);
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'api-authorization': this.token
                },
                body: JSON.stringify(requestBody)
            });
            const data = await response.json();

            // Check both HTTP status and success flag in response
            if (!response.ok || (data.extra_meta && data.extra_meta.success === false)) {
                // Get error message from the response data
                const errorMessage = data.extra_meta?.summary?.title || data.extra_meta?.message || `HTTP error! status: ${response.status}`;

                // Display alert with the error message
                alert(`Check-out failed: ${errorMessage}`);

                const error = new Error(errorMessage);
                error.responseData = data; // Attach the response data to the error for more context
                throw error;
            }

            return { data: data.data || data || {} };
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    /**
     * Verify phone number
     * @param {string} phoneNumber - The phone number to verify
     * @returns {Promise<Object>} - Verification result
     */
    async phonenoVerification(phoneNumber) {

        try {
            let url = `${this.apiurl}/verify-phone`;

            if (phoneNumber) {
                url += `?phone_number=${encodeURIComponent(phoneNumber)}`;
            }

            const response = await fetch(url, {
                headers: {
                    'api-authorization': this.token
                }
            });

            if (!response.ok) {
                // Enhanced error handling with specific error messages
                if (response.status === 404) {
                    throw new Error(`User not found (404)`);
                } else if (response.status === 500) {
                    throw new Error(`Server error (500)`);
                } else {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            }

            const data = await response.json();
            return { data: data.data || data || [] };
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    /**
     * Create a passcode for a user
     * @param {string} phoneNumber - The phone number to create passcode for
     * @param {string} passcode - The passcode to create
     * @returns {Promise<Object>} - Passcode creation result
     */
    async createPasscode(phoneNumber, passcode) {
        try {
            const url = `${this.apiurl}/create-passcode`;
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'api-authorization': this.token
                },
                body: JSON.stringify({
                    phone_number: phoneNumber,
                    passcode: passcode
                })
            });
            const data = await response.json();
            return { data: data.data || data || [] };
        } catch (error) {
            console.error('API Error in createPasscode:', error);
            throw error;
        }
    }

    /**
     * Validate a passcode for a user
     * @param {string} phoneNumber - The phone number
     * @param {string} passcode - The passcode to validate
     * @returns {Promise<Object>} - Validation result
     */
    async validatePasscode(phoneNumber, passcode) {
        try {
            // Build URL with query parameters
            let url = `${this.apiurl}/validate-passcode`;
            const params = new URLSearchParams();

            if (phoneNumber !== null) {
                params.append('phone_number', phoneNumber);
            }

            if (passcode !== null && passcode.trim() !== '') {
                params.append('passcode', passcode);
            }

            // Add query parameters if any exist
            const queryString = params.toString();
            if (queryString) {
                url += `?${queryString}`;
            }

            const response = await fetch(url, {
                headers: {
                    'api-authorization': this.token
                }
            });
            if (response.status == 401) {
                throw new Error(`Invalid passcode!`);
            } else if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return { data: data.data || data || [] };
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
}

// Export as a global variable
window.apiService = new ApiService();