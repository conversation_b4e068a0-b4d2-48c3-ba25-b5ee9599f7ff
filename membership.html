<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">Check-in & Membership</title>
    <!-- Import API Service -->

    <script src="js/api-service.js"></script>
    <!-- Import App Text -->
    <script src="js/app-text.js"></script>
    <!-- Import Crypto Utils -->
    <script src="js/crypto-utils.js"></script>
    <!-- Import Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <!-- Import CSS Styles -->
    <link rel="stylesheet" href="css/membership.css">
</head>

<body>
    <div class="container">
        <div class="header" style="display: flex; justify-content: space-between; align-items: center;">
            <h1 id="appTitle" class="full-title">TopSpin Club Membership Tracker</h1>
            <h1 id="appTitleMobile" class="mobile-title">Club Member Tracker</h1>
            <div style="display: flex; flex-direction: column; align-items: flex-end; min-width: 140px;">
                <span id="userNameDisplay"
                    style="margin-bottom: 6px; font-weight: 700; font-size: 1rem; color: #fff; text-align: right; width: 100%;"></span>
                <button id="logoutBtn"
                    style="width: 100%; padding: 8px 0; font-size: 1rem; cursor: pointer; background: #e74c3c; color: #fff; border: none; border-radius: 4px;">Logout</button>
            </div>
        </div>

        <div class="search-section">
            <label class="search-label" id="searchLabel">Search Member To Get Membership Details</label>
            <div class="search-controls">
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <input type="text" id="searchInput" class="search-input"
                            placeholder="Type 3+ characters to search by name/phone…">
                        <button id="clearSearch" class="clear-search-btn" style="display: none;">×</button>
                    </div>
                    <div id="searchDropdown" class="search-dropdown"></div>
                </div>
            </div>
        </div>

        <!-- Check-ins Section -->
        <div class="section">
            <div class="parallel-grids">
                <!-- Table Tennis Check-ins -->
                <div class="checkin-column">
                    <div class="section-header">
                        <h2 id="tableTennisTitle">Table Tennis Check-ins</h2>
                    </div>
                    <div class="loading" id="loadingTableTennis">Loading Table Tennis check-ins...</div>
                    <div class="checkin-grid" id="tableTennisGrid"></div>
                </div>

                <!-- Gym Check-ins -->
                <div class="checkin-column">
                    <div class="section-header">
                        <h2 id="gymTitle">Gym Check-ins</h2>
                    </div>
                    <div class="loading" id="loadingGym">Loading Gym check-ins...</div>
                    <div class="checkin-grid" id="gymGrid"></div>
                </div>
            </div>
        </div>

        <!-- Membership Section (Hidden by default) -->
        <div class="membership-section" id="membershipSection">
            <h2 id="membershipTitle">Membership Details</h2>
            <div id="membershipGrid" class="membership-grid">
                <div class="no-results" id="selectMemberPrompt">
                    Select a member from search results to view their membership details
                </div>
            </div>
        </div>
    </div>

    <script>
        class SearchMembershipApp {
            constructor() {
                this.searchInput = document.getElementById('searchInput');
                this.searchDropdown = document.getElementById('searchDropdown');
                this.membershipGrid = document.getElementById('membershipGrid');
                this.membershipSection = document.getElementById('membershipSection');
                this.checkinSection = document.getElementById('checkinSection');
                this.checkinGrid = document.getElementById('checkinGrid');
                this.clearSearchBtn = document.getElementById('clearSearch');

                // Initialize API service
                this.apiService = window.apiService;

                // Initialize text content
                this.initTextContent();

                this.searchTimeout = null;
                this.selectedUser = null;

                this.initEventListeners();
                setTimeout(() => {
                    this.loadCheckins();
                    // Also reload membership details for the selected user, if present
                    if (this.selectedUser && this.selectedUser.name) {
                        if (window.loadMembershipDetails && window.displayMembershipDetails) {
                            window.loadMembershipDetails(
                                this.apiService,
                                this.membershipGrid,
                                this.selectedUser,
                                window.displayMembershipDetails.bind(this)
                            );
                        }
                    }
                }, 2000);
            }

            initTextContent() {
                // Set page title
                document.title = window.AppText.pageTitle;

                // Set header text
                document.getElementById('appTitle').textContent = window.AppText.appTitle;
                document.getElementById('appTitleMobile').textContent = window.AppText.appTitleMobile;
                document.getElementById('searchLabel').textContent = window.AppText.searchLabel;
                document.getElementById('tableTennisTitle').textContent = 'Today\'s Table Tennis Check-ins';
                document.getElementById('gymTitle').textContent = 'Today\'s Gym Check-ins';
                document.getElementById('membershipTitle').textContent = window.AppText.membershipTitle;
                document.getElementById('selectMemberPrompt').textContent = window.AppText.selectMemberPrompt;
                document.getElementById('loadingTableTennis').textContent = 'Loading Table Tennis check-ins...';
                document.getElementById('loadingGym').textContent = 'Loading Gym check-ins...';

                // Set search placeholder
                this.searchInput.placeholder = window.AppText.searchPlaceholder;
            }

            initEventListeners() {
                // Search input event
                this.searchInput.addEventListener('input', (e) => {
                    const value = e.target.value.trim();

                    // Show/hide clear button based on input value
                    if (value.length > 0) {
                        this.clearSearchBtn.style.display = 'flex';
                    } else {
                        this.clearSearchBtn.style.display = 'none';
                    }

                    this.handleSearchInput(value);
                });

                // Clear search button click event
                this.clearSearchBtn.addEventListener('click', () => {
                    this.searchInput.value = '';
                    this.clearSearchBtn.style.display = 'none';
                    this.hideSearchDropdown();
                    this.searchInput.focus();

                    // Show check-in section and hide membership section
                    this.checkinSection.style.display = 'block';
                    this.membershipSection.classList.remove('show');
                });

                // Search dropdown click event
                this.searchDropdown.addEventListener('click', (e) => {
                    const searchItem = e.target.closest('.search-item');
                    if (searchItem) {
                        const userName = searchItem.querySelector('.search-item-name').textContent;
                        this.handleUserSelect(userName);
                    }
                });

                // Hide dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.search-container')) {
                        this.hideSearchDropdown();
                    }
                });
            }

            updateSearchPlaceholder() {
                this.searchInput.placeholder = window.AppText.searchPlaceholder;
            }

            async loadCheckins() {
                try {
                    // Load Table Tennis check-ins
                    await this.loadTableTennisCheckins();

                    // Load Gym check-ins
                    await this.loadGymCheckins();
                } catch (error) {
                    console.error('Error loading check-ins:', error);
                }
            }

            async loadTableTennisCheckins() {
                try {
                    // Use the API service to get Table Tennis check-ins
                    const response = await this.apiService.getUserCheckIns(10, 'Table Tennis');
                    console.log(response.data);

                    const tableTennisGrid = document.getElementById('tableTennisGrid');
                    // Hide loading message
                    document.getElementById('loadingTableTennis').style.display = 'none';

                    // Check if response contains data
                    if (response && response.data && response.data.length > 0) {
                        this.displayTableTennisCheckins(response.data);
                    } else {
                        tableTennisGrid.innerHTML = `<div class="no-results">No Table Tennis check-ins available</div>`;
                    }
                } catch (error) {
                    const tableTennisGrid = document.getElementById('tableTennisGrid');
                    tableTennisGrid.innerHTML = `<div class="error">Failed to load Table Tennis check-ins</div>`;
                    // Hide loading message on error
                    document.getElementById('loadingTableTennis').style.display = 'none';
                }
            }

            async loadGymCheckins() {
                try {
                    // Use the API service to get Gym check-ins
                    const response = await this.apiService.getUserCheckIns(10, 'Gym');
                    console.log(response.data);
                    const gymGrid = document.getElementById('gymGrid');
                    // Hide loading message
                    document.getElementById('loadingGym').style.display = 'none';

                    // Check if response contains data
                    if (response && response.data && response.data.length > 0) {
                        this.displayGymCheckins(response.data);
                    } else {
                        gymGrid.innerHTML = `<div class="no-results">No Gym check-ins available</div>`;
                    }
                } catch (error) {
                    const gymGrid = document.getElementById('gymGrid');
                    gymGrid.innerHTML = `<div class="error">Failed to load Gym check-ins</div>`;
                    // Hide loading message on error
                    document.getElementById('loadingGym').style.display = 'none';
                }
            }

            displayTableTennisCheckins(checkins) {
                const tableTennisGrid = document.getElementById('tableTennisGrid');
                if (!checkins || checkins.length === 0) {
                    tableTennisGrid.innerHTML = `<div class="no-results">No Table Tennis check-ins available</div>`;
                    return;
                }

                const html = checkins.map(checkin => {
                    // Get the first character of name for avatar (safely handle empty names)
                    const avatarInitial = checkin.name && checkin.name.length > 0 ?
                        checkin.name.charAt(0).toUpperCase() : '?';

                    // Handle optional email display
                    const emailHtml = checkin.email ?
                        `<div class="checkin-user-email">${checkin.email}</div>` : '';

                    // Handle optional activity display
                    const activity = checkin.activity || checkin.Activity || 'Table Tennis';
                    const activityHtml = activity && activity.trim() ?
                        `<div class="checkin-user-activity"><span class="activity-label">${window.AppText.activityLabel}</span> ${activity}</div>` : '';

                    // Check if user has already checked in today
                    const isCheckedInToday = checkin.isCheckedInToday === true;

                    return `
                    <div class="checkin-card" data-user-name="${checkin.name}" style="cursor: pointer;">
                        <div class="checkin-user-header">
                            <div class="checkin-avatar">
                                ${avatarInitial}
                            </div>
                            <div class="checkin-user-info">
                                <div class="checkin-user-name" data-user-name="${checkin.name}" style="cursor: pointer;">${checkin.name}</div>
                                <div class="checkin-user-phone">${checkin.phone || ''}</div>
                                ${isCheckedInToday ? `
                                ${activityHtml}
                                <div class="checkin-datetime">
                                    <span class="checkin-icon">🕒</span>
                                    ${this.formatCheckInDateTime(checkin.check_in_date, checkin.check_in_time)}
                                </div>
                                ` : ''}
                            </div>
                            <div class="checkin-buttons">
                                ${isCheckedInToday ? `
                                <div class="activity-status table-tennis-status">
                                    <button class="icon-button table-tennis checked-in" data-activity="Table Tennis" data-user-name="${checkin.name}" data-user-id="${checkin.id || ''}" data-checked-in="true">
                                        🏓
                                    </button>
                                </div>` : `
                                <div class="activity-status">
                                    <button class="icon-button table-tennis" data-activity="Table Tennis" data-user-name="${checkin.name}" data-user-id="${checkin.id || ''}" data-checked-in="false">
                                        🏓
                                    </button>
                                </div>`}
                            </div>
                        </div>
                    </div>`;
                }).join('');

                tableTennisGrid.innerHTML = html;
                this.addCheckInButtonListeners();
            }

            displayGymCheckins(checkins) {
                const gymGrid = document.getElementById('gymGrid');
                if (!checkins || checkins.length === 0) {
                    gymGrid.innerHTML = `<div class="no-results">No Gym check-ins available</div>`;
                    return;
                }

                const html = checkins.map(checkin => {
                    // Get the first character of name for avatar (safely handle empty names)
                    const avatarInitial = checkin.name && checkin.name.length > 0 ?
                        checkin.name.charAt(0).toUpperCase() : '?';

                    // Handle optional email display
                    const emailHtml = checkin.email ?
                        `<div class="checkin-user-email">${checkin.email}</div>` : '';

                    // Handle optional activity display
                    const activity = checkin.activity || checkin.Activity || 'Gym';
                    const activityHtml = activity && activity.trim() ?
                        `<div class="checkin-user-activity"><span class="activity-label">${window.AppText.activityLabel}</span> ${activity}</div>` : '';

                    // Check if user has already checked in today
                    const isCheckedInToday = checkin.isCheckedInToday === true;

                    return `
                    <div class="checkin-card" data-user-name="${checkin.name}" style="cursor: pointer;">
                        <div class="checkin-user-header">
                            <div class="checkin-avatar">
                                ${avatarInitial}
                            </div>
                            <div class="checkin-user-info">
                                <div class="checkin-user-name" data-user-name="${checkin.name}" style="cursor: pointer;">${checkin.name}</div>
                                <div class="checkin-user-phone">${checkin.phone || ''}</div>
                                ${isCheckedInToday ? `
                                ${activityHtml}
                                <div class="checkin-datetime">
                                    <span class="checkin-icon">🕒</span>
                                    ${this.formatCheckInDateTime(checkin.check_in_date, checkin.check_in_time)}
                                </div>
                                ` : ''}
                            </div>
                            <div class="checkin-buttons">
                                ${isCheckedInToday ? `
                                <div class="activity-status gym-status">
                                    <button class="icon-button gym checked-in" data-activity="Gym" data-user-name="${checkin.name}" data-user-id="${checkin.id || ''}" data-checked-in="true">
                                        🏋🏻‍♂️
                                    </button>
                                </div>` : `
                                <div class="activity-status">
                                    <button class="icon-button gym" data-activity="Gym" data-user-name="${checkin.name}" data-user-id="${checkin.id || ''}" data-checked-in="false">
                                        🏋🏻‍♂️
                                    </button>
                                </div>`}
                            </div>
                        </div>
                    </div>`;
                }).join('');

                gymGrid.innerHTML = html;
                this.addCheckInButtonListeners();
            }

            // Legacy method for backward compatibility
            displayCheckins(checkins) {
                console.log('Legacy displayCheckins called');
                // Split check-ins by activity and call the appropriate display methods
                const tableTennisCheckins = checkins.filter(checkin => {
                    const activity = checkin.activity || checkin.Activity;
                    return activity === 'Table Tennis';
                });

                const gymCheckins = checkins.filter(checkin => {
                    const activity = checkin.activity || checkin.Activity;
                    return activity === 'Gym';
                });

                this.displayTableTennisCheckins(tableTennisCheckins);
                this.displayGymCheckins(gymCheckins);
            }

            addCheckInButtonListeners() {
                // Add event listeners to all check-in buttons in both grids
                const allCheckInButtons = document.querySelectorAll('#tableTennisGrid .icon-button, #gymGrid .icon-button');
                allCheckInButtons.forEach(button => {
                    button.addEventListener('click', async (event) => {
                        event.stopPropagation(); // Prevent card click event from firing
                        const userName = event.target.getAttribute('data-user-name');
                        const userID = event.target.getAttribute('data-user-id');
                        const activity = event.target.getAttribute('data-activity');

                        await this.handleCheckIn(userName, userID, null, activity);
                    });
                });

                // Add click event listeners to all check-in cards in both grids
                const allCheckinCards = document.querySelectorAll('#tableTennisGrid .checkin-card, #gymGrid .checkin-card');
                allCheckinCards.forEach(card => {
                    card.addEventListener('click', async (event) => {
                        // Ignore clicks on buttons
                        if (event.target.closest('.icon-button')) {
                            return;
                        }

                        const userName = card.getAttribute('data-user-name');
                        const userPhone = card.getAttribute('data-user-phone');
                        const userEmail = card.getAttribute('data-user-email');

                        if (userName) {
                            // Create a more complete user object with available data
                            const user = {
                                name: userName,
                                phone_number: userPhone || '',
                                email: userEmail || ''
                            };

                            // Navigate to membership details
                            await this.selectUser(user);
                        }
                    });
                });
            }

            async handleCheckIn(userName, userID = null, specificButton = null, activity = null) {
                console.log(userName, userID, specificButton, activity);
                try {
                    // Find the button - either the one passed in or find it in the appropriate grid
                    let button = specificButton;

                    if (!button) {
                        // Try to find the button in the table tennis grid or gym grid
                        if (activity === 'Table Tennis') {
                            button = document.getElementById('tableTennisGrid').querySelector(`.icon-button[data-user-name="${userName}"][data-activity="${activity}"]`);
                        } else if (activity === 'Gym') {
                            button = document.getElementById('gymGrid').querySelector(`.icon-button[data-user-name="${userName}"][data-activity="${activity}"]`);
                        }

                        // If still not found, try to find it in the membership grid
                        if (!button && this.membershipGrid) {
                            button = this.membershipGrid.querySelector(`.icon-button[data-user-name="${userName}"][data-activity="${activity}"]`);
                        }
                    }

                    if (button) {
                        button.disabled = true;
                        const originalEmoji = button.textContent.trim();
                        button.textContent = '⏳'; // Loading indicator

                        // Check if user is already checked in using the data-checked-in attribute
                        const isCheckedInToday = button.getAttribute('data-checked-in') === 'true';

                        let response;
                        if (isCheckedInToday) {
                            // If already checked in, call checkOutUser API
                            response = await this.apiService.checkOutUser(userID, activity, localStorage.getItem('staff_id'));
                            // Update button state to indicate checked out
                            button.setAttribute('data-checked-in', 'false');
                        } else {
                            // Otherwise, record check-in for the user with userID if available
                            response = await this.apiService.checkInUser(userName, userID, activity, localStorage.getItem('staff_id'));
                            // Update button state to indicate checked in
                            button.setAttribute('data-checked-in', 'true');
                        }

                        // Update button to show success with a check mark
                        button.innerHTML = '✓';

                        // Toggle the checked-in class based on the new state
                        if (button.getAttribute('data-checked-in') === 'true') {
                            button.classList.add('checked-in');
                        } else {
                            button.classList.remove('checked-in');
                        }

                        // Refresh the check-in list after a short delay
                        setTimeout(() => {
                            this.loadCheckins();
                        }, 2000);
                    }
                } catch (error) {
                    let errorMessage = 'Failed';

                    // Check for response data with extra_meta information
                    if (error.responseData && error.responseData.extra_meta) {
                        const extraMeta = error.responseData.extra_meta;

                        if (extraMeta.slug === 'access_denied') {
                            errorMessage = 'No Booking';
                        } else if (extraMeta.message) {
                            // Use the message from the API if available
                            errorMessage = extraMeta.message.length > 10 ?
                                extraMeta.message.substring(0, 10) + '...' : extraMeta.message;
                        }
                    }
                    // Fallback to HTTP status code errors
                    else if (error.message) {
                        if (error.message.includes('404')) {
                            errorMessage = 'Not Found';
                        } else if (error.message.includes('500')) {
                            errorMessage = 'Server Error';
                        } else if (error.message.includes('401')) {
                            errorMessage = 'Unauthorized';
                        }
                    }

                    // Find the button again in case it wasn't passed in
                    let button = specificButton;
                    if (!button) {
                        // Try to find the button in the appropriate grid
                        if (activity === 'Table Tennis') {
                            button = document.getElementById('tableTennisGrid').querySelector(`.icon-button[data-user-name="${userName}"][data-activity="${activity}"]`);
                        } else if (activity === 'Gym') {
                            button = document.getElementById('gymGrid').querySelector(`.icon-button[data-user-name="${userName}"][data-activity="${activity}"]`);
                        }

                        // If still not found, try to find it in the membership grid
                        if (!button && this.membershipGrid) {
                            button = this.membershipGrid.querySelector(`.icon-button[data-user-name="${userName}"][data-activity="${activity}"]`);
                        }
                    }

                    if (button) {
                        button.textContent = '❌';
                        button.disabled = false;

                        // Reset button after a delay
                        setTimeout(() => {
                            // Set appropriate icon based on activity
                            if (activity === 'Gym') {
                                button.textContent = '🏋🏻‍♂️';
                            } else if (activity === 'Table Tennis') {
                                button.textContent = '🏓';
                            } else {
                                button.textContent = '🏋️';
                            }
                            button.style.backgroundColor = '#22c55e'; // Set green background color
                        }, 2000);
                    }
                }
            }

            formatCheckInDateTime(date, time) {
                if (!time) return 'Just now';

                try {
                    // Format the time
                    const timeParts = time.split(':');
                    let formattedTime = time;

                    if (timeParts.length >= 2) {
                        const hours = parseInt(timeParts[0]);
                        const minutes = timeParts[1].padStart(2, '0'); // Ensure minutes have leading zero
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        const hours12 = hours % 12 || 12;
                        formattedTime = `${hours12}:${minutes} ${ampm}`;
                    }

                    // Format the date if available
                    if (date) {
                        const dateObj = new Date(date);
                        if (!isNaN(dateObj.getTime())) {
                            const today = new Date();
                            const isToday = dateObj.toDateString() === today.toDateString();

                            if (isToday) {
                                return `Today at ${formattedTime}`;
                            } else {
                                const month = dateObj.toLocaleString('default', { month: 'short' });
                                const day = dateObj.getDate();
                                const year = dateObj.getFullYear();
                                const currentYear = today.getFullYear();

                                // Only show year if it's different from current year
                                if (year !== currentYear) {
                                    return `${month} ${day}, ${year} at ${formattedTime}`;
                                } else {
                                    return `${month} ${day} at ${formattedTime}`;
                                }
                            }
                        }
                    }

                    return formattedTime;
                } catch (error) {
                    console.error('Error formatting date/time:', error);
                    return time;
                }
            }

            formatCheckInTime(time) {
                if (!time) return 'Just now';

                try {
                    const timeParts = time.split(':');
                    if (timeParts.length >= 2) {
                        const hours = parseInt(timeParts[0]);
                        const minutes = timeParts[1];
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        const hours12 = hours % 12 || 12;
                        return `${hours12}:${minutes} ${ampm}`;
                    }
                    return time;
                } catch (error) {
                    return time;
                }
            }

            handleSearchInput(value) {
                clearTimeout(this.searchTimeout);

                if (value.length < 3) {
                    this.hideSearchDropdown();
                    return;
                }

                this.searchTimeout = setTimeout(() => {
                    this.performSearch(value);
                }, 300);
            }

            async performSearch(query) {
                this.showSearchLoading();

                try {
                    const response = await this.apiService.searchUsers(query);
                    this.displaySearchResults(response.data);
                } catch (error) {
                    console.error('Search API error:', error);

                    if (error.response && error.response.status === 404) {
                        this.searchDropdown.innerHTML = '<div class="loading">User not found</div>';
                    } else if (error.message && error.message.includes('404')) {
                        this.searchDropdown.innerHTML = '<div class="loading">User not found</div>';
                    } else {
                        this.showSearchError();
                    }
                }
            }

            showSearchLoading() {
                this.searchDropdown.innerHTML = '<div class="loading">Searching...</div>';
                this.searchDropdown.classList.add('show');
            }

            displaySearchResults(results) {
                if (results.length === 0) {
                    this.searchDropdown.innerHTML = '<div class="loading">No results found</div>';
                    return;
                }

                const html = results.map(user => `
                    <div class="search-item" data-phone="${user.phone_number}">
                        <div class="search-item-avatar">
                            ${user.name.charAt(0).toUpperCase()}
                        </div>
                        <div class="search-item-info">
                            <div class="search-item-name">${user.name}</div>
                            <div class="search-item-details">${user.phone_number}${user.email ? ` • ${user.email}` : ''}</div>
                            
                        </div>
                    </div>
                `).join('');

                this.searchDropdown.innerHTML = html;

                // Add click listeners to search items
                this.searchDropdown.querySelectorAll('.search-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const phone = item.dataset.phone;
                        const userData = results.find(u => u.phone_number === phone);
                        // console.log('Selected user:', userData);
                        this.selectUser(userData);
                    });
                });

                this.searchDropdown.classList.add('show');
            }

            showSearchError() {
                this.searchDropdown.innerHTML = '<div class="loading">Error occurred while searching</div>';
            }

            hideSearchDropdown() {
                this.searchDropdown.classList.remove('show');
            }

            async selectUser(user) {
                console.log('Fetching membership details for:', user);
                this.selectedUser = user;

                // Set search input value if phone number is available
                if (user.phone_number) {
                    this.searchInput.value = `${user.name} (${user.phone_number})`;
                } else {
                    this.searchInput.value = user.name;
                }

                this.hideSearchDropdown();

                // Hide check-in section and show membership section
                document.querySelector('.section').style.display = 'none';
                this.membershipSection.classList.add('show');

                // Hide entire search section
                document.querySelector('.search-section').style.display = 'none';

                // Remove top margin from membership section to prevent gap
                this.membershipSection.style.marginTop = '0';

                // Show loading state
                this.membershipGrid.innerHTML = `<div class="loading">${window.AppText.loadingMembership}</div>`;

                try {
                    // Make sure apiService is initialized
                    if (!this.apiService) {
                        this.apiService = window.apiService;
                    }

                    // Fetch membership details from API with user name
                    const response = await this.apiService.getMembershipDetails(user.name);
                    console.log('Membership details:', response.data);
                    this.displayMembershipDetails(response.data);
                } catch (error) {
                    console.error('Error fetching membership details:', error);

                    if (error.response && error.response.status === 404) {
                        this.membershipGrid.innerHTML = `<div class="error">${window.AppText.failedLoadMembership}</div>`;
                    } else if (error.message && error.message.includes('404')) {
                        this.membershipGrid.innerHTML = `<div class="error">${window.AppText.failedLoadMembership}</div>`;
                    } else {
                        this.membershipGrid.innerHTML = `<div class="error">No membership details found for <span style="color: #3b82f6; font-weight: 600;">${user.name}</span></div>`;
                    }
                }
            }

            displayMembershipDetails(data) {
                // console.log('Membership data received:', data);

                // Check if the data has a membership property that is an array
                let memberships = [];
                let userData = null;

                // Extract user data if available
                if (data && data.user) {
                    userData = data.user;
                }

                // Store the current user data for use in event handlers
                this.currentUserData = userData;

                if (data && data.membership && Array.isArray(data.membership)) {
                    // Use the membership array from the response
                    memberships = data.membership;
                } else if (Array.isArray(data)) {
                    // Data is already an array
                    memberships = data;
                } else if (data) {
                    // Single object, convert to array
                    memberships = [data];
                }

                // console.log('Processed memberships array:', memberships);

                if (memberships.length === 0) {
                    this.membershipGrid.innerHTML = `<div class="no-results">${window.AppText.noMembershipFound}</div>`;
                    return;
                }

                // Add user info section with check-in details if available
                let userInfoHtml = '';

                if (userData) {
                    // console.log('User data:', userData.name);

                    // Check if user is checked in for each activity type
                    const isCheckedInGym = userData.recent_check_ins_gym &&
                        userData.recent_check_ins_gym.length > 0 &&
                        userData.recent_check_ins_gym[0].is_checked_in_today;

                    const isCheckedInTT = userData.recent_check_ins_tt &&
                        userData.recent_check_ins_tt.length > 0 &&
                        userData.recent_check_ins_tt[0].is_checked_in_today;

                    // Get the latest check-in info for each activity
                    const gymCheckIn = isCheckedInGym ? userData.recent_check_ins_gym[0] : null;
                    const ttCheckIn = isCheckedInTT ? userData.recent_check_ins_tt[0] : null;

                    // Generate check-in info HTML
                    let checkInInfoHtml = '';
                    if (gymCheckIn) {
                        checkInInfoHtml += `<div>Gym check-in: ${gymCheckIn.check_in_date} at ${gymCheckIn.check_in_time}</div>`;
                    }
                    if (ttCheckIn) {
                        checkInInfoHtml += `<div>Table Tennis check-in: ${ttCheckIn.check_in_date} at ${ttCheckIn.check_in_time}</div>`;
                    }

                    userInfoHtml = `
                        <div class="user-info-card">
                            <div class="user-info-header">
                                <div class="user-info-left">
                                    <div class="user-avatar">${userData.name ? userData.name.charAt(0).toUpperCase() : ''}</div>
                                    <div class="user-name">${userData.name || 'Member'}</div>
                                </div>
                                <div class="user-info-right">
                                    <div class="checkin-buttons">
                                        ${isCheckedInTT ? `
                                        <div class="activity-status table-tennis-status">
                                            <button class="icon-button table-tennis checked-in" data-activity="Table Tennis" data-user-name="${userData.name}" data-user-id="${userData.id || ''}" data-checked-in="true">
                                                🏓
                                            </button>
                                        </div>` : `
                                        <button class="icon-button table-tennis" data-activity="Table Tennis" data-user-name="${userData.name}" data-user-id="${userData.id || ''}" data-checked-in="false">
                                            🏓
                                        </button>`}
                                        ${isCheckedInGym ? `
                                        <div class="activity-status gym-status">
                                            <button class="icon-button gym checked-in" data-activity="Gym" data-user-name="${userData.name}" data-user-id="${userData.id || ''}" data-checked-in="true">
                                                🏋🏻‍♂️
                                            </button>
                                        </div>` : `
                                        <button class="icon-button gym" data-activity="Gym" data-user-name="${userData.name}" data-user-id="${userData.id || ''}" data-checked-in="false">
                                            🏋🏻‍♂️
                                        </button>`}
                                    </div>
                                </div>
                            </div>
                            <div class="user-info-details">
                                ${userData.phone_number ? `<div class="user-detail"><span class="detail-label">${window.AppText.phoneLabel}</span> <a href="tel:${userData.phone_number}" class="phone-link">${userData.phone_number} <span class="call-icon">📞</span></a></div>` : ''}
                                ${userData.email ? `<div class="user-detail"><span class="detail-label">${window.AppText.emailLabel}</span> <span style="word-break: break-word; overflow-wrap: break-word;">${userData.email}</span></div>` : ''}
                                ${isCheckedInGym || isCheckedInTT ? `
                                <div class="check-in-status-container">
                                    ${gymCheckIn && gymCheckIn.is_checked_in_today ? `
                                    <div class="check-in-status gym-check-in">
                                        <div class="check-in-icon">🏋🏻‍♂️</div>
                                        <div class="check-in-details">
                                            <div class="check-in-activity">Gym</div>
                                            <div class="check-in-time">${this.formatCheckInDateTime(gymCheckIn.check_in_date, gymCheckIn.check_in_time)}</div>
                                        </div>
                                    </div>` : ''}
                                    ${ttCheckIn && ttCheckIn.is_checked_in_today ? `
                                    <div class="check-in-status tt-check-in">
                                        <div class="check-in-icon">🏓</div>
                                        <div class="check-in-details">
                                            <div class="check-in-activity">Table Tennis</div>
                                            <div class="check-in-time">${this.formatCheckInDateTime(ttCheckIn.check_in_date, ttCheckIn.check_in_time)}</div>
                                        </div>
                                    </div>` : ''}
                                </div>` : ''}
                            </div>
                        </div>
                    `;
                }

                // Combine user info with membership cards
                let html = userInfoHtml;

                html += memberships.map(membership => `
                    <div class="membership-card" data-membership-id="${membership.membership_id || ''}">
                        <div class="membership-header">
                            <div class="membership-title">${membership.plan_name}</div>
                            <div class="membership-header-right">
                                <div class="membership-status status-${membership.status}">
                                    ${membership.status}
                                </div>
                                <div class="membership-toggle">
                                    <svg class="toggle-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <div class="membership-details-container collapsed">
                            <div class="membership-detail">
                                <div class="membership-detail-label">Start Date:</div>
                                <div class="membership-detail-value">${new Date(membership.start_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                })}</div>
                            </div>
                            
                            <div class="membership-detail">
                                <div class="membership-detail-label">End Date:</div>
                                <div class="membership-detail-value">${new Date(membership.end_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                })}</div>
                            </div>
                            
                            <div class="membership-detail">
                                <div class="membership-detail-label">Duration:</div>
                                <div class="membership-detail-value">${this.calculateDuration(membership.start_date, membership.end_date)}</div>
                            </div>
                            
                            <div class="membership-detail">
                                <div class="membership-detail-label">Amount:</div>
                                <div class="membership-detail-value">₹${(membership.amount).toLocaleString()}</div>
                            </div>
                            
                            <div class="membership-detail">
                                <div class="membership-detail-label">Transaction:</div>
                                <div class="membership-detail-value">${membership.transaction_type.charAt(0).toUpperCase() + membership.transaction_type.slice(1)}</div>
                            </div>
                            
                            <div class="membership-detail">
                                <div class="membership-detail-label">Coaching Plan:</div>
                                <div class="membership-detail-value">${membership.is_coaching_plan === 'yes' ? 'Yes' : 'No'}</div>
                            </div>
                        </div>
                    </div>
                `).join('');

                this.membershipGrid.innerHTML = html;

                // Add event listeners to the check-in buttons in the user info section
                const membershipButtons = this.membershipGrid.querySelectorAll('.icon-button');
                // console.log('Found membership buttons:', membershipButtons.length);

                membershipButtons.forEach(button => {
                    // console.log('Adding listener to button:', button.outerHTML);
                    button.addEventListener('click', async (event) => {
                        event.preventDefault();
                        event.stopPropagation();

                        const userName = button.getAttribute('data-user-name');
                        const userID = button.getAttribute('data-user-id');
                        const activity = button.getAttribute('data-activity');




                        await this.handleCheckIn(userName, userID, button, activity);
                    });
                });

                // Add click event listeners to membership headers for expand/collapse functionality
                this.initMembershipCardToggle();

            }

            calculateDuration(startDate, endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                const months = Math.floor(diffDays / 30);
                const days = diffDays % 30;

                if (months > 0) {
                    return days > 0 ? `${months} months, ${days} days` : `${months} months`;
                }
                return `${days} days`;
            }

            initMembershipCardToggle() {
                // Find all membership card headers
                const membershipCards = this.membershipGrid.querySelectorAll('.membership-card');

                membershipCards.forEach(card => {
                    const header = card.querySelector('.membership-header');
                    const detailsContainer = card.querySelector('.membership-details-container');

                    if (header && detailsContainer) {
                        // Set initial max-height to 0 (collapsed)
                        detailsContainer.style.maxHeight = '0px';

                        // Add click event listener to the header
                        header.addEventListener('click', (event) => {
                            // Prevent event bubbling
                            event.stopPropagation();

                            // Toggle expanded class on THIS card only
                            card.classList.toggle('expanded');

                            // Toggle collapsed class on THIS details container only
                            detailsContainer.classList.toggle('collapsed');

                            // Set max-height based on expanded state for THIS container only
                            if (detailsContainer.classList.contains('collapsed')) {
                                detailsContainer.style.maxHeight = '0px';
                            } else {
                                // Set max-height to the scroll height to allow animation
                                detailsContainer.style.maxHeight = detailsContainer.scrollHeight + 'px';
                            }
                        });
                    }
                });
            }

            formatCheckInDate(date, time) {
                if (!date) return '';

                try {
                    // Format date in a user-friendly way
                    const checkInDate = new Date(date);
                    const formattedDate = checkInDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    // Format time if available
                    let formattedTime = '';
                    if (time) {
                        const timeParts = time.split(':');
                        if (timeParts.length >= 2) {
                            const hours = parseInt(timeParts[0]);
                            const minutes = timeParts[1];
                            const ampm = hours >= 12 ? 'PM' : 'AM';
                            const hours12 = hours % 12 || 12;
                            formattedTime = ` at ${hours12}:${minutes} ${ampm}`;
                        }
                    }

                    return `${formattedDate}${formattedTime}`;
                } catch (error) {
                    console.error('Error formatting check-in date:', error);
                    return date + (time ? ` ${time}` : '');
                }
            }
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', () => {
            // Show logged-in user's name
            const userName = localStorage.getItem('name');
            if (userName) {
                const userNameDisplay = document.getElementById('userNameDisplay');
                if (userNameDisplay) {
                    userNameDisplay.textContent = userName;
                }
            }
            // Logout button logic
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', () => {
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('hasPasscode');
                    localStorage.removeItem('loginTimestamp');
                    localStorage.removeItem('userPhone');
                    localStorage.removeItem('name');
                    localStorage.removeItem('staff_id');
                    sessionStorage.clear();
                    window.location.replace('login.html');
                });
            }
            // Check authentication status
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            const hasPasscode = localStorage.getItem('hasPasscode') === 'true';

            // Store that user has accessed the main app
            if (isLoggedIn) {
                sessionStorage.setItem('accessedMainApp', 'true');
            }

            if (!isLoggedIn) {
                // Redirect to login if not logged in
                window.location.replace('login.html');
                return;
            }

            // Auto-logout if login is older than 30 days
            const loginTimestamp = parseInt(localStorage.getItem('loginTimestamp'), 10);
            const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000;
            // const THIRTY_DAYS_MS = 15 * 1000; // 1 minute for testing
            const now = Date.now();
            if (!loginTimestamp || now - loginTimestamp > THIRTY_DAYS_MS) {
                // Clear authentication data
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('hasPasscode');
                localStorage.removeItem('loginTimestamp');
                localStorage.removeItem('userPhone');
                localStorage.removeItem('name');
                localStorage.removeItem('staff_id');

                sessionStorage.clear();
                window.location.replace('login.html');
                return;
            }

            // Continue with passcode checks
            if (hasPasscode) {
                // Check if this is a direct access (not coming from passcode verification)
                const fromPasscode = sessionStorage.getItem('fromPasscode') === 'true';
                const accessedMainApp = sessionStorage.getItem('accessedMainApp') === 'true';

                // Only redirect if user has never entered passcode or accessed the main app
                if (!fromPasscode && !accessedMainApp) {
                    // Redirect to passcode verification
                    window.location.replace('passcode.html');
                    return;
                }
            }

            // Initialize the app if authenticated
            new SearchMembershipApp();


        });
    </script>
</body>

</html>