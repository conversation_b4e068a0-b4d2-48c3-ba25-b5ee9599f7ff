# TopSpin Club Membership Tracker - Technical Documentation

## Table of Contents
1. [Introduction](#introduction)
2. [System Architecture](#system-architecture)
3. [Authentication System](#authentication-system)
4. [Frontend Components](#frontend-components)
5. [API Integration](#api-integration)
6. [Data Flow](#data-flow)
7. [Security Implementation](#security-implementation)
8. [Persistent Login System](#persistent-login-system)
9. [Deployment Guide](#deployment-guide)
10. [Maintenance and Troubleshooting](#maintenance-and-troubleshooting)

## Introduction

The TopSpin Club Membership Tracker is a web-based application designed for club staff to manage member check-ins, search for members, and view membership details. This document provides a comprehensive technical overview of the application's architecture, components, and implementation details.

### Key Features
- Secure authentication with phone number and 4-digit passcode
- Persistent login across sessions
- Member search functionality
- Membership details viewing
- Check-in management for different activities (Table Tennis/Gym)
- Responsive design for various devices

## System Architecture

The application follows a client-side architecture with direct API communication:

```
┌─────────────────┐     ┌───────────────┐     ┌─────────────────┐
│                 │     │               │     │                 │
│  Client Browser │────▶│  Static Files │     │  TopSpin Club   │
│                 │     │  (HTML/JS/CSS)│     │  API Server     │
│                 │◀────│               │     │                 │
└─────────────────┘     └───────┬───────┘     └────────┬────────┘
                                │                      │
                                │                      │
                                ▼                      ▼
                        ┌───────────────┐     ┌─────────────────┐
                        │               │     │                 │
                        │  Local Node.js│     │  Database       │
                        │  Server      │     │                 │
                        │  (Optional)  │     │                 │
                        └───────────────┘     └─────────────────┘
```

### Technology Stack
- **Frontend**: HTML5, CSS3, Vanilla JavaScript (ES6+)
- **Server**: Node.js (optional, for static file serving)
- **API Communication**: Fetch API
- **Storage**: localStorage and sessionStorage for client-side data persistence
- **Security**: Client-side encryption for sensitive data

## Authentication System

The authentication system implements a two-step process:

1. **Phone Number Verification**
   - User enters their phone number
   - System verifies against the TopSpin Club API
   - Upon successful verification, user proceeds to passcode setup or entry

2. **Passcode Authentication**
   - First-time users set up a 4-digit passcode
   - Returning users enter their existing passcode
   - Passcode is validated against the API

### Authentication Flow

```
┌─────────┐     ┌──────────────┐     ┌───────────────┐     ┌─────────────┐
│         │     │              │     │               │     │             │
│  Login  │────▶│ Phone Number │────▶│ Passcode      │────▶│ Membership  │
│  Screen │     │ Verification │     │ Setup/Entry   │     │ Screen      │
│         │     │              │     │               │     │             │
└─────────┘     └──────────────┘     └───────────────┘     └─────────────┘
     ▲                                                            │
     │                                                            │
     └────────────────────────────────────────────────────────────┘
                          Session Expiration
```

### Session Management
- **localStorage**: Stores persistent authentication data (isLoggedIn, hasPasscode, loginTimestamp)
- **sessionStorage**: Stores temporary session flags (fromPasscode, accessedMainApp)
- **Automatic Login**: Checks for valid persistent login on application start

## Frontend Components

### HTML Structure
- **login.html**: Phone number verification screen
- **passcode-setup.html**: First-time passcode creation
- **passcode.html**: Passcode verification for returning users
- **membership.html**: Main application interface

### CSS Components
- **membership.css**: Styling for the main application interface
- **login.css**: Styling for authentication screens

### JavaScript Components
- **login.js**: Handles phone number verification and initial authentication
- **passcode-setup.js**: Manages passcode creation flow
- **passcode.js**: Handles passcode verification
- **api-service.js**: Centralizes all API communication
- **app-text.js**: Contains all application text content
- **crypto-utils.js**: Provides encryption/decryption utilities

### Key Classes and Objects
- **ApiService**: Handles all API communication
- **SearchMembershipApp**: Core application logic in membership.html
- **AppText**: Centralized text content management

## API Integration

The application communicates with the TopSpin Club API for all data operations:

### API Endpoints
1. **Phone Verification**: Validates user phone number
   ```
   GET /api/v1/phone-verification
   ```

2. **Passcode Validation**: Validates user passcode
   ```
   POST /api/v1/validate-passcode
   ```

3. **User Search**: Searches for users by name or phone
   ```
   GET /api/v1/user-details?name={query}
   ```

4. **Membership Details**: Retrieves detailed membership information
   ```
   GET /api/v1/user-membership-details?user_id={id}
   ```

5. **Check-in**: Records user check-in for an activity
   ```
   POST /api/v1/check-in
   ```

6. **Recent Check-ins**: Retrieves recent club check-ins
   ```
   GET /api/v1/checked-in-users
   ```

### API Authentication
- API requests include an authentication token in the header
- Different tokens are used for development and production environments
- Token is stored in the ApiService class

## Data Flow

### Authentication Data Flow
1. User enters phone number
2. Phone number is sent to API for verification
3. If verified, user creates or enters passcode
4. Passcode is validated against API
5. Upon successful validation, user data and session flags are stored
6. User is redirected to the main application

### Member Search Data Flow
1. User enters search query
2. Query is sent to API with debounce (to prevent excessive requests)
3. API returns matching members
4. Results are displayed in the UI
5. User selects a member to view details

### Check-in Data Flow
1. User selects a member and activity
2. Check-in request is sent to API
3. API processes check-in and returns status
4. UI is updated to reflect check-in status

## Security Implementation

### Client-side Security
- Sensitive data is stored with encryption
- Authentication tokens are never exposed in the UI
- Session expiration after 30 days of inactivity

### API Security
- All API requests require authentication token
- Rate limiting to prevent abuse
- HTTPS for secure communication

### Data Protection
- No sensitive member data is stored locally
- Session data is cleared on logout
- Automatic session expiration

## Persistent Login System

The application implements a persistent login system that allows users to remain logged in across browser sessions:

### Implementation Details
1. **Initial Login**:
   - User logs in with phone number
   - System stores authentication data in localStorage:
     - isLoggedIn: true
     - hasPasscode: true
     - loginTimestamp: current timestamp
     - userPhone: phone number
     - name: user's name
     - staff_id: user's staff ID

2. **Session Validation**:
   - On application start, system checks:
     - If user has valid localStorage credentials
     - If loginTimestamp is within 30 days
   - If valid, user bypasses login screens
   - If invalid, user must log in again

3. **Session Refresh**:
   - loginTimestamp is updated on successful passcode entry
   - This extends the persistent login for another 30 days

4. **Session Flags**:
   - fromPasscode: Indicates user came from passcode screen
   - accessedMainApp: Indicates user has accessed main app in current session

### Code Implementation
```javascript
// Check for persistent login
const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
const hasPasscode = localStorage.getItem('hasPasscode') === 'true';
const loginTimestamp = parseInt(localStorage.getItem('loginTimestamp') || '0');
const now = Date.now();
const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000;

// If valid persistent login exists
if (isLoggedIn && hasPasscode && loginTimestamp && (now - loginTimestamp < THIRTY_DAYS_MS)) {
    // Set session flags to bypass passcode screen
    sessionStorage.setItem('fromPasscode', 'true');
    sessionStorage.setItem('accessedMainApp', 'true');
    window.location.replace('membership.html');
}
```

## Deployment Guide

### Prerequisites
- Web server with HTTPS support
- Node.js (optional, for local development)

### Deployment Steps
1. **Clone the repository**:
   ```
   git clone [repository-url]
   ```

2. **Configure API endpoints**:
   - Edit `js/api-service.js` to set the correct API URL and token
   - Development: `https://topspinclub-stagapi.devpress.net/api/v1`
   - Production: `https://api.topspinclub.in/api/v1`

3. **Deploy to web server**:
   - Upload all files to your web server
   - Ensure proper file permissions

4. **Configure web server**:
   - Set up HTTPS
   - Configure server to serve `login.html` as the default page
   - Set appropriate cache headers

5. **Test deployment**:
   - Verify all pages load correctly
   - Test authentication flow
   - Test API communication

### Local Development
1. **Install dependencies**:
   ```
   npm install
   ```

2. **Start local server**:
   ```
   node server.js
   ```

3. **Access application**:
   ```
   http://localhost:3000
   ```

## Maintenance and Troubleshooting

### Common Issues

1. **API Connection Failures**:
   - Check network connectivity
   - Verify API token is valid
   - Ensure API endpoints are correct

2. **Authentication Issues**:
   - Clear browser localStorage and sessionStorage
   - Verify user credentials in the API
   - Check for API changes that might affect authentication

3. **UI Rendering Problems**:
   - Verify CSS files are loaded correctly
   - Check browser console for JavaScript errors
   - Test in different browsers to isolate browser-specific issues

## Developers

| Role             | Name                                                                 |
|------------------|--------------------------------------------------------------------------|
| Frontend         | Manish Mundra                                                            |
| Backend PHP API  | Kamesh Valodara                                                          |


## Document Versioning Details
Version: 1.0  
Last Updated: June 26, 2025  
Author: Manish  Mundra
