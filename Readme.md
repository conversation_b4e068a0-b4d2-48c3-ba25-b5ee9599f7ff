# TopSpin Club Membership Tracker

**This README provides a summary and quickstart.**

## Overview

The TopSpin Club Membership Tracker is a secure web application for club staff to:
1. Log in using phone number verification and a 4-digit passcode
2. View recent check-ins (Table Tennis and Gym) in separate lists
3. Search for members by name or phone number
4. View detailed membership information by tapping on user entries
5. Check-in members to activities
6. Check-out members from activities

The app is a client-side web application that communicates directly with the TopSpin Club API. **Authentication is handled via phone verification and a 4-digit passcode, with persistent login for 30 days and local credential encryption for enhanced security.**

## Technical Architecture

### Frontend Components

The application is built using vanilla JavaScript with a class-based architecture:

- **HTML Structure**: 
  - `passcode-setup.html` - 4-digit passcode creation screen
  - `passcode.html` - Passcode verification screen
  - `membership.html` - Main application with dynamic content loading
- **CSS**: 
  - `css/membership.css` - Styling for main application
  - `css/login.css` - Styling for authentication screens
- **JavaScript**: 
  - `SearchMembershipApp` class in `membership.html` handles the core application logic
  - `ApiService` class in `js/api-service.js` handles all API communication with the TopSpin Club API
  - `AppText` object in `js/app-text.js` centralizes all text content for easier maintenance
  - `crypto-utils.js` provides encryption/decryption utilities for secure credential storage

### Server Component

- **Node.js Server (Optional)**: `server.js` can be used for static file serving if needed, but is no longer required for API proxying. The frontend communicates directly with the TopSpin Club API.

### API Endpoints

The application communicates directly with the following TopSpin Club API endpoints:

1. `/api/checked-in-users` - Get recent check-ins
2. `/api/user-details` - Search for users by name/phone
3. `/api/user-membership-details` - Get membership details for a specific user
4. `/api/check-in` - Record a new check-in for a user

## Application Features

### Authentication System

- Secure login with phone number verification
- 4-digit passcode setup for quick access
- Encrypted storage of credentials and passcode in localStorage
- Persistent login for 30 days (users stay logged in across browser sessions)
- Intelligent authentication flow that prevents unnecessary redirects
- Automatic redirection to login if not authenticated or session expired
- Professional UI with TopSpin Club branding

### Check-in and Check-out Display

- Shows recent club check-ins with user information in separate lists for Table Tennis and Gym
- Displays user name, phone, and check-in time
- Provides activity-specific check-in and check-out buttons (Table Tennis/Gym)
- Intelligently shows/hides activity icons based on check-in status:
  - If checked in for Gym, hides Table Tennis icon
  - If checked in for Table Tennis, hides Gym icon
  - If not checked in, shows both activity options
- Handles already checked-in users with disabled buttons
- Tapping on a user navigates to their detailed membership information

### Member Search

- Real-time search as you type (with debounce)
- Searches by name or phone number
- Displays matching members in a dropdown
- Shows member details including last check-in time

### Membership Details

- Displays comprehensive membership information
- Shows membership type, status, and duration
- Includes start/end dates and days remaining
- Provides payment information
- Allows activity-specific check-in directly from membership view
- Intelligently shows/hides activity icons based on check-in status (same logic as check-in list)

## Code Structure

### Authentication Components

1. **Login System**
   - `login.html` - Phone number verification
   - Local storage for persistent login (30 days)
   - Branded UI with TopSpin Club logo

2. **Passcode System**
   - `passcode-setup.html` - 4-digit passcode creation
   - `passcode.html` - Quick access via passcode
   - Session validation and persistence across browser sessions

### SearchMembershipApp Class

The main application class with the following key methods:

1. **Constructor & Initialization**
   - `constructor()` - Sets up the application
   - `initTextContent()` - Applies text content from AppText
   - `initEventListeners()` - Sets up event handlers
   - `checkAuthentication()` - Validates user session

2. **Check-in and Check-out Functionality**
   - `loadCheckins()` - Fetches and displays recent check-ins in separate lists for Table Tennis and Gym
   - `displayCheckins()` - Renders check-in data to the UI in categorized lists
   - `handleCheckIn()` - Processes user check-in requests
   - `handleCheckOut()` - Processes user check-out requests

3. **Search Functionality**
   - `handleSearchInput()` - Processes search input with debounce
   - `performSearch()` - Executes the API search
   - `displaySearchResults()` - Renders search results

4. **Membership Display**
   - `displayMembershipDetails()` - Shows membership information
   - `formatDate()`, `calculateDuration()` - Helper methods for date formatting

### Text Content Management

All static text is centralized in the `AppText` object in `js/app-text.js`, making it easier to:
- Maintain consistent terminology
- Update text content in one place
- Prepare for future localization if needed

## Data Flow

1. User accesses the application
   - System checks for existing valid persistent login
   - If valid login exists, user bypasses authentication screens
   - If no valid login, server serves login.html as the entry point
   - User enters phone number for verification
   - Credentials are encrypted and stored locally with a 30-day expiration

2. First-time login flow
   - User is directed to passcode-setup.html
   - User creates a 4-digit passcode
   - Passcode is encrypted and stored locally
   - User is redirected to the main application

3. Returning user flow
   - User is directed to passcode.html
   - User enters their 4-digit passcode
   - System decrypts stored passcode and validates the entry
   - User is redirected to the main application

4. Main application initialization
   - Authentication status is checked
   - Application initializes and loads recent check-ins
   - Activity-specific icons are displayed based on check-in status

5. User searches for a member
   - Input triggers search after debounce
   - Results display in dropdown
   - User selects a member

6. Application displays membership details
   - Fetches membership data from API
   - Renders membership cards
   - Shows user information
   - Displays activity-specific check-in options

7. User checks in or checks out a member
   - Activity-specific check-in/check-out request sent to API
   - UI updates to show success
   - Check-in lists refresh with separate displays for Table Tennis and Gym
   - Activity icons update based on check-in status

8. User taps on a checked-in member
   - Application navigates to the member's detailed information page
   - Displays comprehensive membership details
   - Shows check-in history and current status

## Configuration

The application uses environment variables stored in `.env`:
- `API_BASE_URL` - Base URL for the TopSpin Club API
- `API_KEY` - Authentication key for API access
- `PORT` - Local server port (default: 3000)

## Maintenance and Extension

### Adding New Features

To add new features:
1. Update the HTML structure in `membership.html` if needed
2. Add new methods to the `SearchMembershipApp` class
3. Add any new API endpoints to `ApiService`
4. Add new text strings to `AppText`

### Modifying Text Content

All text content should be modified in `js/app-text.js` to maintain centralization.

### Styling Changes

CSS styles are defined in `css/membership.css`. The application uses a responsive design that works on both desktop and mobile devices.

## Browser Compatibility

The application is designed to work with modern browsers including:
- Chrome
- Firefox
- Safari

## Security Considerations

- Direct API communication with the TopSpin Club API
- Persistent login has a 30-day expiration for security
- Login timestamp is refreshed on successful passcode verification
- User credentials and passcode are encrypted before storing in localStorage
- Encryption/decryption utilities handle secure data storage
- Session flags are stored in sessionStorage for current-session data
- Persistent authentication data is stored in localStorage with expiration checks

## Deployment

To deploy the application:
1. Ensure Node.js is installed (optional, only if using the Node.js server)
2. Configure the API endpoints in `js/api-service.js`
3. If using Node.js server: Run `npm install`
4. If using Node.js server: Start the server with `npm start`
   - The server listens on all network interfaces (`0.0.0.0`), so the app is accessible from other devices on your network.
5. Access the app at `https://members.spinclub.in/` (production) or locally via `http://localhost:3000` (development)


## UI Enhancements

- **Consistent Branding**: TopSpin Club logo integrated throughout the application
- **Dynamic Copyright Year**: Footer automatically displays the current year
- **Activity-Specific Icons**: Visual indicators for Table Tennis and Gym activities
- **Responsive Design**: Works on both desktop and mobile devices
- **Professional Authentication UI**: Clean, branded login and passcode screens
- **Intuitive Navigation**: Clear user flow from login to main application


## Developers

| Role             | Name/Link                                                                 |
|------------------|--------------------------------------------------------------------------|
| Frontend         | Manish Mundra                                                            |
| Backend PHP API  | Kamesh Valodara                                                          |
| Git Repository Staging   | [TopSpin Membership Web Workflow](http://git.indianic.com/n8n/workflow-indianic-org/tree/topspin_membership_web_workflow) |
| Git Repository Live  | [TopSpin Membership Web Workflow](https://gitlab.indianic.com/server5/spin-members) |

## Support

For technical support or questions about the application, please contact:
- Manish Mundra (<EMAIL>)

## Last Updated

July 2, 2025
