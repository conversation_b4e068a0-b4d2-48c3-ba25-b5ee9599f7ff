/* TopSpin Club Membership Tracker Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins';
    background: #f8fafc;
    min-height: 100vh;
    padding: 0;
    margin: 0;
    color: #1e293b;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: #ffffff;
    min-height: 100vh;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 25px 40px;
    margin: 0 0 40px 0;
    border-bottom: 1px solid #e2e8f0;
}

/* Title styling */
.full-title {
    display: block;
}

.mobile-title {
    display: none;
    font-size: 1.3rem;
    /* Smaller font size for mobile title */
}

/* Parallel grids layout */
.parallel-grids {
    display: flex;
    flex-direction: row;
    gap: 20px;
    width: 100%;
}

.checkin-column {
    flex: 1;
    min-width: 0;
    /* Prevents flex items from overflowing */
    padding: 0;
}

/* Media query for mobile devices */
@media screen and (max-width: 768px) {
    .full-title {
        display: none;
    }

    .mobile-title {
        display: block;
    }

    /* Stack columns on mobile */
    .parallel-grids {
        flex-direction: column;
    }
}

.header h1 {
    color: white;
    font-size: 2rem;
    margin: 0;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.header p {
    color: rgba(255, 255, 255, 0.9);
    margin: 8px 0 0 0;
    font-size: 1rem;
}

.search-section {
    margin: 0 40px 40px 40px;
}

.search-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.search-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    align-items: flex-end;
}

.dropdown {
    position: relative;
    min-width: 140px;
}

.dropdown select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
}

.dropdown select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-container {
    position: relative;
    flex: 1;
}

.search-input-wrapper {
    position: relative;
    width: 100%;
}

.search-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    padding-right: 40px;
    /* Space for clear button */
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
    color: #374151;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 21l-4.35-4.35M18 10.5a7.5 7.5 0 11-15 0 7.5 7.5 0 0115 0z'/%3e%3c/svg%3e");
    background-position: 16px center;
    background-repeat: no-repeat;
    background-size: 20px;
}

.clear-search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-search-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e5e7eb;
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 8px 8px;
    max-height: 320px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.search-dropdown.show {
    display: block;
}

.search-item {
    padding: 16px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.15s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-item:hover {
    background-color: #f8fafc;
}

.search-item:last-child {
    border-bottom: none;
}

.search-item-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.search-item-info {
    flex: 1;
}

.search-item-name {
    font-weight: 600;
    color: #111827;
    font-size: 14px;
    margin-bottom: 2px;
}

.search-item-details {
    color: #6b7280;
    font-size: 13px;
}

.search-item-checkin {
    font-size: 12px;
    color: #3b82f6;
    display: flex;
    align-items: center;
}

.search-item-checkin:before {
    content: '\1F552';
    /* Clock emoji */
    margin-right: 4px;
    font-size: 11px;
}

.loading {
    padding: 24px;
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

/* Check-in section styles */
.section {
    margin: 0 40px 40px 40px;
}

.checkin-section {
    margin: 40px 40px 0 40px;
    padding-top: 0;
}

.checkin-section h2 {
    color: #111827;
    margin-bottom: 24px;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkin-section h2::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 2px;
}

.checkin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 10px;
}

.checkin-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.checkin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: #87b1f5;
}

.checkin-user-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.checkin-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.checkin-user-info {
    flex: 1;
}

.checkin-user-name {
    font-weight: 600;
    color: #111827;
    font-size: 16px;
    margin-bottom: 2px;
}

.checkin-user-phone {
    color: #6b7280;
    font-size: 13px;
    margin-bottom: 4px;
}

.checkin-user-email {
    color: #6b7280;
    font-size: 13px;
    margin-bottom: 2px;
}

.checkin-user-activity {
    color: #3b82f6;
    font-size: 13px;
    margin-top: 2px;
}

.activity-label {
    font-weight: 500;
}

.checkin-datetime {
    color: #3b82f6;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
}

.checkin-icon {
    font-size: 12px;
}

.checkin-button {
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    margin-left: 12px;
}

.checkin-button:hover {
    background: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.checkin-button:active {
    transform: translateY(1px);
    box-shadow: none;
}

.checkin-button.checked-in {
    background-color: #1e40af;
    cursor: default;
    opacity: 0.8;
}

.checkin-button.checked-in:hover {
    transform: none;
    box-shadow: none;
}

/* Icon button styles */
.checkin-buttons {
    display: flex;
    gap: 8px;
    margin-left: 12px;
}

.icon-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon-button:active {
    transform: translateY(1px);
    box-shadow: none;
}

.icon-button[disabled] {
    opacity: 0.6;
    cursor: default;
}

.icon-button[disabled]:hover {
    transform: none;
    box-shadow: none;
}

.icon-button.checked-in {
    opacity: 1;
    cursor: default;
    transform: none;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5), 0 0 0 4px rgba(59, 130, 246, 0.3);
}

/* Pastel colors for activity buttons */
.icon-button.table-tennis {
    background-color: #ffccd5;
    /* Pastel pink */
    color: #e63946;
}

.icon-button.table-tennis:hover {
    background-color: #ffc2cc;
}

.icon-button.table-tennis.checked-in {
    background-color: #ffa8b5;
    /* Darker pastel pink */
    color: #c71f2d;
    border: 2px solid #e63946;
}

.icon-button.gym {
    background-color: #c1e1c1;
    /* Pastel green */
    color: #2a9d8f;
}

.icon-button.gym:hover {
    background-color: #b7ddb7;
}

.icon-button.gym.checked-in {
    background-color: #a3d9a3;
    /* Darker pastel green */
    color: #1a7268;
    border: 2px solid #2a9d8f;
}

.membership-section {
    margin: 40px 40px 0 40px;
    padding-top: 0;
    /* border-top: 1px solid #e5e7eb; */
    display: none;
    /* Hidden by default */
}

.back-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    margin-right: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.back-button svg {
    width: 20px;
    height: 20px;
}

.membership-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.membership-header h2 {
    margin: 0;
    margin-bottom: 0 !important;
    display: flex;
    align-items: center;
}

.membership-section.show {
    display: block;
}

.membership-section h2 {
    color: #111827;
    margin-bottom: 24px;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.membership-section h2::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
}

.membership-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 24px;
    align-items: flex-start;
    /* Prevent vertical stretching */
}

.user-info-card {
    width: 100%;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.user-info-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.user-info-left {
    display: flex;
    align-items: center;
}

.user-info-right {
    margin-left: auto;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3b82f6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 12px;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
}

.user-info-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.user-detail {
    display: flex;
    font-size: 14px;
    color: #4b5563;
}

.detail-label {
    font-weight: 500;
    margin-right: 8px;
    color: #6b7280;
    min-width: 80px;
}

.last-checkin {
    color: #3b82f6;
}

/* Activity status styles */
.activity-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 5px;
}

.activity-icon.checked-in {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 20px;
    margin-bottom: 4px;
}

.table-tennis-status .activity-icon.checked-in {
    background-color: #ffa8b5;
    color: #c71f2d;
    border: 2px solid #e63946;
}

.gym-status .activity-icon.checked-in {
    background-color: #a3d9a3;
    color: #1a7268;
    border: 2px solid #2a9d8f;
}

.activity-label {
    font-size: 12px;
    font-weight: 500;
    color: #4b5563;
}

/* Check-in status container */
.check-in-status-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 10px;
}

.check-in-status {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    width: 100%;
    max-width: 350px;
}

.gym-check-in {
    background-color: rgba(193, 225, 193, 0.3);
    border: 1px solid rgba(42, 157, 143, 0.2);
}

.tt-check-in {
    background-color: rgba(255, 204, 213, 0.3);
    border: 1px solid rgba(230, 57, 70, 0.2);
}

.check-in-icon {
    font-size: 24px;
    margin-right: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.gym-check-in .check-in-icon {
    background-color: #c1e1c1;
    color: #2a9d8f;
}

.tt-check-in .check-in-icon {
    background-color: #ffccd5;
    color: #e63946;
}

.check-in-details {
    flex: 1;
}

.check-in-activity {
    font-weight: 600;
    font-size: 14px;
    color: #111827;
}

.check-in-time {
    font-size: 13px;
    color: #6b7280;
}

.membership-card {
    flex: 1 1 100%;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
}

/* Tablet - 2 boxes per row */
@media (min-width: 768px) {
    .membership-card {
        flex: 1 1 calc(50% - 16px);
    }
}

/* Desktop - 3 boxes per row */
@media (min-width: 1024px) {
    .membership-card {
        flex: 1 1 calc(33.333% - 16px);
        /* Ensure each card maintains its own context */
        height: auto;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }

    /* Ensure details container stays within its parent card */
    .membership-card .membership-details-container {
        overflow: hidden;
        transition: max-height 0.3s ease;
        width: 100%;
    }
}

.membership-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: #3b82f6;
}

.membership-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    cursor: pointer;
    min-height: 32px;
}

.membership-header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.membership-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.membership-toggle:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.membership-card.expanded .toggle-icon {
    transform: rotate(180deg);
}

.membership-details-container {
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.membership-details-container.collapsed {
    max-height: 0;
}

.membership-title {
    font-size: 1rem;
    font-weight: 600;
    color: #111827;
    display: flex;
    align-items: center;
}

/* Custom Alert Styles */
.custom-alert-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.custom-alert-overlay.show {
    opacity: 1;
    visibility: visible;
}

.custom-alert {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 400px;
    padding: 0;
    overflow: hidden;
    transform: translateY(20px);
    transition: transform 0.3s;
}

.custom-alert-overlay.show .custom-alert {
    transform: translateY(0);
}

.custom-alert-header {
    background-color: #1e40af;
    color: white;
    padding: 16px 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.custom-alert-header.error {
    background-color: #ef4444;
}

.custom-alert-header.success {
    background-color: #10b981;
}

.custom-alert-body {
    padding: 20px;
    font-size: 0.95rem;
    line-height: 1.5;
}

.custom-alert-footer {
    padding: 12px 20px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    text-align: right;
}

.custom-alert-btn {
    background-color: #1e40af;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.custom-alert-btn:hover {
    background-color: #1e3a8a;
}

.membership-status {
    padding: 3px 10px;
    border-radius: 16px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
}

.membership-detail {
    margin-bottom: 8px;
    font-size: 13px;
    display: flex;
    align-items: flex-start;
    gap: 6px;
}

.membership-detail-label {
    font-weight: 600;
    color: #374151;
    min-width: 80px;
}

.membership-detail-value {
    color: #6b7280;
    flex: 1;
}

.membership-features {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f3f4f6;
}

.membership-features h4 {
    font-size: 13px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.membership-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.membership-features li {
    padding: 4px 0;
    font-size: 12px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 6px;
}

.membership-features li::before {
    content: '✓';
    color: #10b981;
    font-weight: 600;
    font-size: 12px;
}

.status-running {
    background-color: #10b981;
    color: white;
}

.status-expired {
    background-color: #ef4444;
    color: white;
}

.status-pending {
    background-color: #f59e0b;
    color: white;
}

.status-paused {
    background-color: #6366f1;
    color: white;
}

.no-results {
    text-align: center;
    color: #6b7280;
    padding: 60px 20px;
    font-size: 14px;
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
}

.error {
    text-align: center;
    color: #ef4444;
    padding: 60px 20px;
    font-size: 14px;
    background: #fef2f2;
    border: 2px dashed #fca5a5;
    border-radius: 12px;
}

@media (max-width: 768px) {
    .search-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .dropdown select {
        min-width: auto;
    }

    .membership-grid {
        grid-template-columns: 1fr;
    }

    .header h1 {
        font-size: 2rem;
    }

    .checkin-grid {
        grid-template-columns: 1fr;
    }

    /* More compact check-in cards for small devices */
    .checkin-card {
        padding: 10px;
        margin-bottom: 10px;
    }

    .checkin-user-header {
        margin-bottom: 6px;
        gap: 8px;
    }

    .checkin-avatar {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .checkin-user-name {
        font-size: 14px;
        margin-bottom: 1px;
    }

    .checkin-user-phone,
    .checkin-user-email,
    .checkin-user-activity {
        font-size: 12px;
        margin-bottom: 2px;
    }

    .checkin-datetime {
        font-size: 11px;
        margin-top: 2px;
    }

    .checkin-button {
        padding: 6px 12px;
        font-size: 12px;
        margin-left: 6px;
    }
}

/* Phone link styling */
.phone-link {
    color: #3b82f6;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
    padding: 2px 6px;
    border-radius: 4px;
}

.phone-link:hover {
    background-color: #dbeafe;
    color: #1d4ed8;
}

.call-icon {
    margin-left: 6px;
    font-size: 0.9em;
}