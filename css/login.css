/* Login Screen Styles */

.login-page {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8fafc;
    padding: 20px;
}

.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 480px;
}

.login-logo-container {
    text-align: center;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    padding: 15px;
    border-radius: 10px;
    width: 100%;
    margin-bottom: 20px;
}

.login-logo {
    max-width: 180px;
    height: auto;
    filter: brightness(1.1) contrast(1.1);
}

.login-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    padding: 40px;
    width: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

.login-card h2 {
    color: #2d3748;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-align: center;
}

.login-subtitle {
    color: #718096;
    text-align: center;
    margin-bottom: 32px;
    font-size: 0.95rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    color: #a0aec0;
    font-size: 16px;
}

.form-group input {
    padding: 16px 16px 16px 48px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 15px;
    width: 100%;
    transition: all 0.2s ease;
    background-color: #f8fafc;
}

.form-group input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
    background-color: #fff;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a5568;
    cursor: pointer;
}

.remember-me input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
}

.forgot-password {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.forgot-password:hover {
    color: #1e40af;
    text-decoration: underline;
}

.login-button {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 240px;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(59, 130, 246, 0.2);
}

.login-button:active {
    transform: translateY(1px);
}

.login-error {
    color: #e53e3e;
    font-size: 14px;
    margin-top: 16px;
    text-align: center;
    min-height: 20px;
    font-weight: 500;
}

.login-footer {
    margin-top: 24px;
    text-align: center;
    color: #718096;
    font-size: 14px;
}

/* Passcode Screen Styles */
.passcode-container {
    display: none;
    /* Hidden by default */
    flex-direction: column;
    align-items: center;
    padding: 20px;
    min-height: 60vh;
}

.passcode-container.show {
    display: flex;
}

.passcode-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 32px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.passcode-title {
    color: #111827;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.passcode-subtitle {
    color: #6b7280;
    margin-bottom: 24px;
    font-size: 0.9rem;
}

.passcode-input-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 24px;
}

.passcode-digit {
    width: 50px;
    height: 60px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s ease;
}

.passcode-digit:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.passcode-button {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.passcode-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.passcode-button:active {
    transform: translateY(1px);
}

.passcode-error {
    color: #ef4444;
    font-size: 14px;
    margin-top: 16px;
    min-height: 20px;
}

.passcode-success {
    color: #10b981;
    font-size: 14px;
    margin-top: 16px;
    min-height: 20px;
}

/* Responsive adjustments */
@media (max-width: 480px) {

    .login-card,
    .passcode-card {
        padding: 24px;
    }

    .passcode-digit {
        width: 40px;
        height: 50px;
        font-size: 20px;
    }
}